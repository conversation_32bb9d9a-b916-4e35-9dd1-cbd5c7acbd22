<!DOCTYPE html>
<html>
<head>
  <title>Convert SVG to PNG</title>
</head>
<body>
  <h1>SVG to PNG Converter</h1>
  <p>This page will convert the SVG icon to PNG files of different sizes.</p>
  
  <img id="svgImage" src="icon.svg" style="display: none;">
  
  <div>
    <h2>16x16 Icon:</h2>
    <canvas id="canvas16" width="16" height="16"></canvas>
    <a id="download16" href="#" download="icon16.png">Download 16x16</a>
  </div>
  
  <div>
    <h2>48x48 Icon:</h2>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <a id="download48" href="#" download="icon48.png">Download 48x48</a>
  </div>
  
  <div>
    <h2>128x128 Icon:</h2>
    <canvas id="canvas128" width="128" height="128"></canvas>
    <a id="download128" href="#" download="icon128.png">Download 128x128</a>
  </div>
  
  <script>
    // Wait for the image to load
    document.getElementById('svgImage').onload = function() {
      const img = this;
      
      // Convert to 16x16
      const canvas16 = document.getElementById('canvas16');
      const ctx16 = canvas16.getContext('2d');
      ctx16.drawImage(img, 0, 0, 16, 16);
      document.getElementById('download16').href = canvas16.toDataURL('image/png');
      
      // Convert to 48x48
      const canvas48 = document.getElementById('canvas48');
      const ctx48 = canvas48.getContext('2d');
      ctx48.drawImage(img, 0, 0, 48, 48);
      document.getElementById('download48').href = canvas48.toDataURL('image/png');
      
      // Convert to 128x128
      const canvas128 = document.getElementById('canvas128');
      const ctx128 = canvas128.getContext('2d');
      ctx128.drawImage(img, 0, 0, 128, 128);
      document.getElementById('download128').href = canvas128.toDataURL('image/png');
    };
  </script>
</body>
</html>
