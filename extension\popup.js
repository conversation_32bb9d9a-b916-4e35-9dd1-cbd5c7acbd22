// Popup script for GigBus Snippet Collector

// API endpoint
const API_URL = 'http://localhost:8000';
const APP_URL = 'http://localhost:8501';

// DOM elements
const sessionSelect = document.getElementById('sessionSelect');
const saveButton = document.getElementById('saveButton');
const openAppButton = document.getElementById('openAppButton');
const refreshButton = document.getElementById('refreshButton');
const statusMessage = document.getElementById('statusMessage');

// Load sessions when popup opens
document.addEventListener('DOMContentLoaded', () => {
  loadSessions();
  
  // Load current session from storage
  chrome.storage.local.get(['currentSessionId'], (result) => {
    if (result.currentSessionId) {
      sessionSelect.value = result.currentSessionId;
    }
  });
});

// Save button click handler
saveButton.addEventListener('click', () => {
  const sessionId = sessionSelect.value;
  
  if (!sessionId) {
    showStatus('Please select a session', 'error');
    return;
  }
  
  // Save to storage
  chrome.storage.local.set({ currentSessionId: sessionId }, () => {
    showStatus('Session saved!', 'success');
    
    // Clear any badge
    chrome.action.setBadgeText({ text: '' });
  });
});

// Open app button click handler
openAppButton.addEventListener('click', () => {
  chrome.tabs.create({ url: APP_URL });
});

// Refresh button click handler
refreshButton.addEventListener('click', () => {
  loadSessions();
});

// Function to load sessions from API
function loadSessions() {
  sessionSelect.innerHTML = '<option value="">Loading sessions...</option>';
  
  fetch(`${API_URL}/extension/sessions`)
    .then(response => response.json())
    .then(data => {
      if (data.success && data.sessions && data.sessions.length > 0) {
        // Clear the select
        sessionSelect.innerHTML = '';
        
        // Add a default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '-- Select a session --';
        sessionSelect.appendChild(defaultOption);
        
        // Add options for each session
        data.sessions.forEach(session => {
          const option = document.createElement('option');
          option.value = session.id;
          option.textContent = session.title;
          sessionSelect.appendChild(option);
        });
        
        // Load current session from storage
        chrome.storage.local.get(['currentSessionId'], (result) => {
          if (result.currentSessionId) {
            sessionSelect.value = result.currentSessionId;
          }
        });
      } else {
        sessionSelect.innerHTML = '<option value="">No sessions available</option>';
        showStatus('No sessions found. Create a session in GigBus first.', 'info');
      }
    })
    .catch(error => {
      console.error('Error loading sessions:', error);
      sessionSelect.innerHTML = '<option value="">Error loading sessions</option>';
      showStatus('Error connecting to GigBus. Make sure the app is running.', 'error');
    });
}

// Function to show status message
function showStatus(message, type) {
  statusMessage.textContent = message;
  statusMessage.className = `status ${type}`;
  statusMessage.classList.remove('hidden');
  
  // Hide after 3 seconds
  setTimeout(() => {
    statusMessage.classList.add('hidden');
  }, 3000);
}
