#!/usr/bin/env python3
"""
Advanced test script for GigBus browser extension improvements
Tests the new LLM integration and analysis features
"""

import requests
import json
import time
import sys

# Configuration
API_URL = "http://localhost:8000"
APP_URL = "http://localhost:8501"

def test_api_connection():
    """Test if the API is running"""
    try:
        response = requests.get(f"{API_URL}/sessions/")
        if response.status_code == 200:
            print("✅ API connection successful")
            return True
        else:
            print(f"❌ API connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API connection error: {str(e)}")
        return False

def test_session_creation():
    """Test creating a new session for advanced features"""
    try:
        session_data = {
            "title": f"Advanced Features Test {int(time.time())}"
        }
        response = requests.post(f"{API_URL}/sessions/", json=session_data)
        
        if response.status_code == 200:
            session = response.json()
            print(f"✅ Session created successfully: {session['title']} (ID: {session['id']})")
            return session
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Session creation error: {str(e)}")
        return None

def test_llm_text_sending(session_id):
    """Test sending text to LLM endpoint"""
    try:
        test_prompt = "Explain the benefits of artificial intelligence in simple terms."
        response = requests.post(f"{API_URL}/research/generate", json={"prompt": test_prompt})
        
        if response.status_code == 200:
            data = response.json()
            content = data.get("content", "")
            if content:
                print(f"✅ LLM text sending working: Generated {len(content)} characters")
                return content
            else:
                print("❌ LLM returned empty content")
                return None
        else:
            print(f"❌ LLM text sending failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ LLM text sending error: {str(e)}")
        return None

def test_analysis_endpoint(session_id):
    """Test the analysis endpoint"""
    try:
        analysis_data = {
            "session_id": session_id,
            "title": "Test Sentiment Analysis",
            "content": "## Sentiment Analysis\n\nSentiment: Positive (Score: 3)\nPositive words found: 3\nNegative words found: 0\n\n---\n*Analysis performed by GigBus Extension*",
            "analysis_type": "sentiment_analysis"
        }
        
        response = requests.post(f"{API_URL}/extension/add-analysis", json=analysis_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Analysis endpoint working")
                return result.get("snippet_id")
            else:
                print(f"❌ Analysis endpoint failed: {result.get('error')}")
                return None
        else:
            print(f"❌ Analysis endpoint failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Analysis endpoint error: {str(e)}")
        return None

def test_code_execution():
    """Test the code execution functionality"""
    try:
        # Import the function from app.py
        import sys
        sys.path.append('.')
        from app import execute_analysis_code
        
        test_code = """
# Simple word count
words = len(text.split())
characters = len(text)
result = f"Words: {words}, Characters: {characters}"
"""
        
        test_text = "This is a test sentence for code execution."
        result = execute_analysis_code(test_code, test_text)
        
        if "Words:" in result and "Characters:" in result:
            print("✅ Code execution working")
            print(f"   Result: {result}")
            return True
        else:
            print(f"❌ Code execution failed: {result}")
            return False
    except Exception as e:
        print(f"❌ Code execution error: {str(e)}")
        return False

def test_analysis_operations():
    """Test the browser-side analysis operations"""
    try:
        # Test sentiment analysis
        test_text = "This is a wonderful and amazing product that I absolutely love!"
        
        # Simulate the sentiment analysis logic from the extension
        positive_words = ['wonderful', 'amazing', 'love', 'absolutely']
        negative_words = ['bad', 'terrible', 'awful', 'hate']
        
        text_lower = test_text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > 0:
            print("✅ Sentiment analysis logic working")
            print(f"   Positive words found: {positive_count}")
            
            # Test word count analysis
            words = len(test_text.split())
            characters = len(test_text)
            
            if words > 0 and characters > 0:
                print("✅ Word count analysis logic working")
                print(f"   Words: {words}, Characters: {characters}")
                return True
            else:
                print("❌ Word count analysis logic failed")
                return False
        else:
            print("❌ Sentiment analysis logic failed")
            return False
    except Exception as e:
        print(f"❌ Analysis operations error: {str(e)}")
        return False

def test_extension_files():
    """Test that all extension files exist and have the right content"""
    import os
    
    required_files = [
        'extension/manifest.json',
        'extension/background.js',
        'extension/popup.html',
        'extension/popup.js',
        'extension/content.js'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
            
            # Check for specific content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if file_path == 'extension/manifest.json':
                if '"version": "1.2"' in content:
                    print("   ✅ Version updated to 1.2")
                else:
                    print("   ❌ Version not updated")
                    all_exist = False
                    
            elif file_path == 'extension/background.js':
                if 'analyzeText' in content and 'executeAnalysis' in content:
                    print("   ✅ Analysis functionality added")
                else:
                    print("   ❌ Analysis functionality missing")
                    all_exist = False
                    
            elif file_path == 'extension/content.js':
                if 'ANALYSIS_OPERATIONS' in content and 'sentiment' in content:
                    print("   ✅ Analysis operations defined")
                else:
                    print("   ❌ Analysis operations missing")
                    all_exist = False
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_app_enhancements():
    """Test that app.py has the new features"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = [
            ('send_text_to_llm', 'LLM text sending function'),
            ('execute_analysis_code', 'Code execution function'),
            ('Analysis Results', 'Analysis results tab'),
            ('Send to LLM', 'Send to LLM button'),
            ('Generate Code', 'Code generation feature')
        ]
        
        all_found = True
        for feature, description in features:
            if feature in content:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} missing")
                all_found = False
        
        return all_found
    except Exception as e:
        print(f"❌ App enhancements test error: {str(e)}")
        return False

def run_advanced_tests():
    """Run all advanced feature tests"""
    print("🚀 Starting GigBus Advanced Features Tests\n")
    
    tests = [
        ("API Connection", test_api_connection),
        ("Extension Files", test_extension_files),
        ("App Enhancements", test_app_enhancements),
        ("Analysis Operations", test_analysis_operations),
        ("Code Execution", test_code_execution)
    ]
    
    passed = 0
    total = len(tests)
    session = None
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {str(e)}")
    
    # Additional tests that require a session
    if passed >= 3:  # Only run if basic tests pass
        print(f"\n📋 Testing Session Creation...")
        session = test_session_creation()
        if session:
            passed += 1
            print(f"✅ Session Creation PASSED")
            
            print(f"\n📋 Testing LLM Integration...")
            if test_llm_text_sending(session['id']):
                passed += 1
                print(f"✅ LLM Integration PASSED")
                
                print(f"\n📋 Testing Analysis Endpoint...")
                if test_analysis_endpoint(session['id']):
                    passed += 1
                    print(f"✅ Analysis Endpoint PASSED")
                else:
                    print(f"❌ Analysis Endpoint FAILED")
            else:
                print(f"❌ LLM Integration FAILED")
        else:
            print(f"❌ Session Creation FAILED")
        
        total += 3  # Add the additional tests to total
    
    print(f"\n🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= total - 1:  # Allow for 1 failure
        print("🎉 Advanced features are ready for testing!")
        print("\n📝 New Features Available:")
        print("1. ✨ Send edited text to LLM as prompt")
        print("2. 🔍 Right-click text analysis (Sentiment, Word Count, Readability, Keywords)")
        print("3. 🤖 Code generation and execution for analysis")
        print("4. 🔗 Analysis results as redirection prompts")
        print("5. 📊 Comprehensive analysis synthesis")
        print("6. 🎯 Analysis results concatenation with existing content")
        
        print("\n🚀 How to test:")
        print("1. Start API: python api.py")
        print("2. Start Streamlit: streamlit run app.py")
        print("3. Load extension in Chrome (version 1.2)")
        print("4. Select text on any webpage and right-click")
        print("5. Choose 'Analyze Text' and pick an analysis type")
        print("6. Check the 'Analysis Results' tab in GigBus")
        print("7. Use 'Send to LLM' button in text editor")
        print("8. Try 'Use as Redirection' and 'Generate Code' features")
        
        if session:
            print(f"\n🔗 Test session created: ID {session['id']}")
        
        return True
    else:
        print("⚠️ Some advanced features may not work properly. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_advanced_tests()
    sys.exit(0 if success else 1)
