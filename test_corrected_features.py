#!/usr/bin/env python3
"""
Test script for the corrected GigBus implementation
Tests analysis features in the text editor (not browser-based)
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported"""
    try:
        import streamlit as st
        print("✅ Streamlit import successful")
        
        import requests
        print("✅ Requests import successful")
        
        from datetime import datetime
        print("✅ Datetime import successful")
        
        # Test app.py imports
        sys.path.append('.')
        import app
        print("✅ App module import successful")
        
        # Test API imports
        import api
        print("✅ API module import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        return False

def test_analysis_functions():
    """Test the analysis functions in app.py"""
    try:
        from app import (
            perform_sentiment_analysis,
            perform_word_count_analysis,
            perform_readability_analysis,
            perform_keyword_analysis,
            execute_analysis_code,
            send_text_to_llm
        )
        
        test_text = "This is a wonderful and amazing test text for analysis. It contains multiple sentences and should provide good results for testing our analysis functions."
        
        # Test sentiment analysis
        sentiment_result = perform_sentiment_analysis(test_text)
        if "Sentiment:" in sentiment_result and "Positive" in sentiment_result:
            print("✅ Sentiment analysis function working")
        else:
            print(f"❌ Sentiment analysis function failed: {sentiment_result}")
            
        # Test word count analysis
        wordcount_result = perform_word_count_analysis(test_text)
        if "Words:" in wordcount_result and "Characters:" in wordcount_result:
            print("✅ Word count analysis function working")
        else:
            print(f"❌ Word count analysis function failed: {wordcount_result}")
            
        # Test readability analysis
        readability_result = perform_readability_analysis(test_text)
        if "Readability Score:" in readability_result:
            print("✅ Readability analysis function working")
        else:
            print(f"❌ Readability analysis function failed: {readability_result}")
            
        # Test keyword analysis
        keyword_result = perform_keyword_analysis(test_text)
        if "Top Keywords:" in keyword_result:
            print("✅ Keyword analysis function working")
        else:
            print(f"❌ Keyword analysis function failed: {keyword_result}")
            
        # Test code execution
        test_code = """
result = f"Test execution successful. Text length: {len(text)}"
"""
        execution_result = execute_analysis_code(test_code, test_text)
        if "Test execution successful" in execution_result:
            print("✅ Code execution function working")
        else:
            print(f"❌ Code execution function failed: {execution_result}")
        
        return True
    except Exception as e:
        print(f"❌ Analysis functions error: {str(e)}")
        return False

def test_file_structure():
    """Test that all required files exist and are properly structured"""
    required_files = [
        'app.py',
        'api.py',
        'extension/manifest.json',
        'extension/background.js',
        'extension/popup.html',
        'extension/popup.js',
        'extension/README.md'
    ]
    
    # Files that should NOT exist (removed)
    removed_files = [
        'extension/content.js'
    ]
    
    all_correct = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_correct = False
    
    for file_path in removed_files:
        if not os.path.exists(file_path):
            print(f"✅ {file_path} correctly removed")
        else:
            print(f"❌ {file_path} should be removed")
            all_correct = False
    
    return all_correct

def test_extension_manifest():
    """Test that the extension manifest is properly configured"""
    try:
        import json
        with open('extension/manifest.json', 'r') as f:
            manifest = json.load(f)
        
        # Check version
        if manifest.get('version') == '1.2':
            print("✅ Extension version is 1.2")
        else:
            print(f"❌ Extension version incorrect: {manifest.get('version')}")
        
        # Check permissions (should NOT have scripting)
        permissions = manifest.get('permissions', [])
        if 'scripting' not in permissions:
            print("✅ Scripting permission correctly removed")
        else:
            print("❌ Scripting permission should be removed")
        
        # Check content scripts (should NOT exist)
        content_scripts = manifest.get('content_scripts')
        if content_scripts is None:
            print("✅ Content scripts correctly removed")
        else:
            print("❌ Content scripts should be removed")
        
        # Check host permissions (should be minimal)
        host_permissions = manifest.get('host_permissions', [])
        if len(host_permissions) == 1 and 'localhost:8000' in host_permissions[0]:
            print("✅ Host permissions correctly configured")
        else:
            print(f"❌ Host permissions incorrect: {host_permissions}")
        
        return True
    except Exception as e:
        print(f"❌ Manifest test error: {str(e)}")
        return False

def test_background_script():
    """Test that background script is simplified"""
    try:
        with open('extension/background.js', 'r') as f:
            content = f.read()
        
        # Should NOT contain analysis-related code
        analysis_terms = ['executeAnalysis', 'analyzeSentiment', 'analyzeText', 'ANALYSIS_OPERATIONS']
        has_analysis = any(term in content for term in analysis_terms)
        
        if not has_analysis:
            print("✅ Background script correctly simplified (no analysis code)")
        else:
            print("❌ Background script still contains analysis code")
        
        # Should contain basic snippet functionality
        if 'addToGigBus' in content and 'extension/add-snippet' in content:
            print("✅ Background script has basic snippet functionality")
        else:
            print("❌ Background script missing basic snippet functionality")
        
        return not has_analysis
    except Exception as e:
        print(f"❌ Background script test error: {str(e)}")
        return False

def test_app_structure():
    """Test that app.py has the correct structure"""
    try:
        with open('app.py', 'r') as f:
            content = f.read()
        
        # Should have analysis functions
        analysis_functions = [
            'perform_sentiment_analysis',
            'perform_word_count_analysis', 
            'perform_readability_analysis',
            'perform_keyword_analysis'
        ]
        
        all_functions_present = all(func in content for func in analysis_functions)
        if all_functions_present:
            print("✅ App has all analysis functions")
        else:
            print("❌ App missing some analysis functions")
        
        # Should have text editor with analysis tools
        if 'Analysis Tools:' in content and 'Sentiment Analysis' in content:
            print("✅ App has analysis tools in text editor")
        else:
            print("❌ App missing analysis tools in text editor")
        
        # Should NOT have Analysis Results tab
        if 'Analysis Results' not in content or 'tab4' not in content:
            print("✅ Analysis Results tab correctly removed")
        else:
            print("❌ Analysis Results tab should be removed")
        
        return all_functions_present
    except Exception as e:
        print(f"❌ App structure test error: {str(e)}")
        return False

def run_corrected_tests():
    """Run all tests for the corrected implementation"""
    print("🚀 Starting GigBus Corrected Implementation Tests\n")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Extension Manifest", test_extension_manifest),
        ("Background Script", test_background_script),
        ("App Structure", test_app_structure),
        ("Analysis Functions", test_analysis_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {str(e)}")
    
    print(f"\n🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The corrected implementation is ready.")
        print("\n📝 Corrected Features:")
        print("1. ✨ Analysis tools moved to text editor (not browser-based)")
        print("2. 🔍 Sentiment, Word Count, Readability, Keyword analysis in app")
        print("3. 🤖 Code generation and execution for analysis")
        print("4. 📝 Send edited text to LLM as prompt")
        print("5. 🎯 Simplified browser extension (snippet collection only)")
        print("6. 🧹 Removed unnecessary browser analysis features")
        
        print("\n🚀 How to use:")
        print("1. Start API: python api.py")
        print("2. Start Streamlit: streamlit run app.py")
        print("3. Load extension in Chrome")
        print("4. In GigBus app, enable 'Text Editing Mode'")
        print("5. Use analysis tools in the right panel")
        print("6. Use 'Send to LLM' for prompt-based redirection")
        
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_corrected_tests()
    sys.exit(0 if success else 1)
