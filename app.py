import streamlit as st
import requests
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("app")

# API URL
API_URL = "http://localhost:8000"

# Page configuration
st.set_page_config(page_title="Deep Research Assistant", layout="wide")

# Initialize session state variables
if "current_session_id" not in st.session_state:
    st.session_state.current_session_id = None
if "research_content" not in st.session_state:
    st.session_state.research_content = ""
if "streaming" not in st.session_state:
    st.session_state.streaming = False
if "progress_items" not in st.session_state:
    st.session_state.progress_items = []
if "browser_snippets" not in st.session_state:
    st.session_state.browser_snippets = []
if "selected_progress" not in st.session_state:
    st.session_state.selected_progress = None
if "redirection_point" not in st.session_state:
    st.session_state.redirection_point = 0
if "redirection_points" not in st.session_state:
    st.session_state.redirection_points = []
if "edit_mode" not in st.session_state:
    st.session_state.edit_mode = False
if "edited_content" not in st.session_state:
    st.session_state.edited_content = ""

# Helper functions
def get_sessions() -> List[Dict[str, Any]]:
    """Get all research sessions from the API"""
    try:
        logger.info(f"Fetching sessions from {API_URL}/sessions/")
        response = requests.get(f"{API_URL}/sessions/")
        logger.info(f"Sessions API response status: {response.status_code}")

        if response.status_code == 200:
            sessions = response.json()
            logger.info(f"Retrieved {len(sessions)} sessions: {sessions}")
            return sessions

        logger.error(f"Failed to get sessions: {response.status_code} - {response.text}")
        return []
    except Exception as e:
        logger.error(f"Error getting sessions: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def create_session(title: str) -> Optional[Dict[str, Any]]:
    """Create a new research session"""
    try:
        # First check if a session with this title already exists
        logger.info(f"Checking if session with title '{title}' already exists")
        existing_sessions = get_sessions()
        logger.info(f"Found {len(existing_sessions)} existing sessions")

        for session in existing_sessions:
            if session["title"].lower() == title.lower():
                logger.info(f"Session with title '{title}' already exists, returning existing session with ID {session['id']}")
                return session  # Return existing session instead of creating a new one

        # If no existing session found, create a new one
        logger.info(f"Creating new session with title '{title}'")
        response = requests.post(f"{API_URL}/sessions/", json={"title": title})

        logger.info(f"API response status: {response.status_code}")
        if response.status_code == 200:
            session_data = response.json()
            logger.info(f"Created new session: {title} (ID: {session_data['id']})")
            return session_data

        logger.error(f"Failed to create session: {response.status_code} - {response.text}")
        return None
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def get_progress_items(session_id: int) -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/progress/session/{session_id}")
    if response.status_code == 200:
        return response.json()
    return []

def create_progress(session_id: int, content: str) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/progress/", json={
        "session_id": session_id,
        "content": content
    })
    if response.status_code == 200:
        return response.json()
    return None

def get_snippets(session_id: int) -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/snippets/session/{session_id}")
    if response.status_code == 200:
        return response.json()
    return []

def create_snippet(session_id: int, content: str, source_url: str = None) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/snippets/", json={
        "session_id": session_id,
        "content": content,
        "source_url": source_url
    })
    if response.status_code == 200:
        return response.json()
    return None

def create_redirection(session_id: int, progress_id: int, redirection_point: int,
                      new_instruction: str, browser_snippet_id: int = None) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/redirect/", json={
        "session_id": session_id,
        "progress_id": progress_id,
        "redirection_point": redirection_point,
        "new_instruction": new_instruction,
        "browser_snippet_id": browser_snippet_id
    })
    if response.status_code == 200:
        return response.json()
    return None

def apply_multiple_redirections(content: str, redirection_points: List[Dict], session_id: int) -> str:
    """Apply multiple redirections to content while preserving unchanged parts"""
    if not redirection_points:
        return content

    # Sort redirection points by position (descending to avoid index shifting)
    sorted_points = sorted(redirection_points, key=lambda x: x['position'], reverse=True)

    result_content = content
    for point in sorted_points:
        position = point['position']
        instruction = point['instruction']

        # Split content at redirection point
        before = result_content[:position]
        after = result_content[position:]

        # Generate new content for this redirection
        redirect_prompt = f"{before}\n\nContinue from here: {instruction}"

        try:
            # Make API call for redirection
            response = requests.post(
                f"{API_URL}/research/generate",
                json={"prompt": redirect_prompt},
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                data = response.json()
                new_content = data.get("content", "")
                # Replace the content from redirection point onwards
                result_content = new_content + after
            else:
                logger.error(f"Failed to generate redirection content: {response.status_code}")
        except Exception as e:
            logger.error(f"Error in redirection: {str(e)}")

    return result_content

def highlight_text_with_redirection_points(content: str, redirection_points: List[Dict]) -> str:
    """Add visual markers for redirection points in the text"""
    if not redirection_points:
        return content

    # Sort by position (descending to avoid index shifting)
    sorted_points = sorted(redirection_points, key=lambda x: x['position'], reverse=True)

    result = content
    for i, point in enumerate(sorted_points):
        position = point['position']
        marker = f" **[REDIRECTION POINT {len(sorted_points)-i}]** "
        result = result[:position] + marker + result[position:]

    return result

def send_text_to_llm(text: str) -> Optional[str]:
    """Send text to LLM and get response"""
    try:
        response = requests.post(
            f"{API_URL}/research/generate",
            json={"prompt": text},
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            data = response.json()
            return data.get("content", "")
        else:
            logger.error(f"Failed to send text to LLM: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error sending text to LLM: {str(e)}")
        return None

def execute_analysis_code(code: str, text: str) -> str:
    """Execute analysis code safely and return results"""
    try:
        # Create a safe execution environment
        safe_globals = {
            '__builtins__': {
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'list': list,
                'dict': dict,
                'sum': sum,
                'max': max,
                'min': min,
                'round': round,
                'abs': abs,
                'print': print,
            },
            'text': text,
            'result': None
        }

        # Execute the code
        exec(code, safe_globals)

        # Return the result
        return str(safe_globals.get('result', 'No result returned'))
    except Exception as e:
        return f"Error executing code: {str(e)}"

def ensure_session_loaded(session_id: int):
    """Ensure that all data for a session is properly loaded into session state"""
    if session_id is not None:
        logger.info(f"Loading session data for session ID: {session_id}")

        # Update the current session ID
        st.session_state.current_session_id = session_id
        logger.info(f"Set current_session_id to {session_id}")

        # Load progress items
        progress_items = get_progress_items(session_id)
        st.session_state.progress_items = progress_items
        logger.info(f"Loaded {len(progress_items)} progress items")

        # Load browser snippets
        browser_snippets = get_snippets(session_id)
        st.session_state.browser_snippets = browser_snippets
        logger.info(f"Loaded {len(browser_snippets)} browser snippets")

        return True

    logger.warning(f"Cannot load session data: session_id is None")
    return False

def stream_research(prompt: str, placeholder, redirection_data=None, context=None):
    """Stream research content and update the UI in real-time with support for redirection"""
    # Use a non-async function to avoid issues with Streamlit's event loop
    st.session_state.streaming = True
    st.session_state.research_content = ""

    # Create a direct output area that will always be visible
    output_area = st.empty()
    output_area.markdown("**Starting research...**")

    # Create a status indicator and a progress bar
    status = st.info("Starting research stream...")
    progress_bar = st.progress(0)

    # Prepare request data
    request_data = {
        "prompt": prompt
    }

    # Add redirection data if provided
    if redirection_data:
        request_data["redirection"] = redirection_data

    # Add context if provided
    if context:
        request_data["context"] = context

    try:
        # Make a direct request to generate content (non-streaming)
        # This is more reliable than streaming with Streamlit
        response = requests.post(
            f"{API_URL}/research/generate",
            json={"prompt": prompt},
            headers={"Content-Type": "application/json"}
        )

        if response.status_code != 200:
            st.error(f"Error: {response.status_code} - {response.text}")
            status.error("Research failed!")
            st.session_state.streaming = False
            return

        # Get the content from the response
        data = response.json()
        content = data.get("content", "")

        # Simulate streaming by showing content gradually
        total_length = len(content)
        chunk_size = max(1, total_length // 20)  # Show content in about 20 chunks

        for i in range(0, total_length, chunk_size):
            # Update progress
            progress_value = min(i / total_length, 0.99)
            progress_bar.progress(progress_value)

            # Get the next chunk
            current_content = content[:i+chunk_size]

            # Update the content display
            output_area.markdown(current_content)

            # Update the session state
            st.session_state.research_content = current_content

            # Update the placeholder
            placeholder.markdown(current_content)

            # Small delay to simulate streaming
            time.sleep(0.1)

        # Final update
        progress_bar.progress(1.0)
        status.success("Research complete!")
        output_area.markdown(content)
        st.session_state.research_content = content
        placeholder.markdown(content)

    except Exception as e:
        error_msg = f"Error in research: {str(e)}"
        st.error(error_msg)
        status.error("Research failed!")
        import traceback
        st.error(traceback.format_exc())
    finally:
        st.session_state.streaming = False

    # Save the research content to the database
    if st.session_state.current_session_id and st.session_state.research_content:
        create_progress(st.session_state.current_session_id, st.session_state.research_content)
        # Update progress items
        st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

    # Return the final content
    return st.session_state.research_content

# UI Components
st.title("Deep Research Assistant with Gemini Integration")

# Sidebar for session management
with st.sidebar:
    st.header("Research Sessions")

    # Create new session
    new_session_title = st.text_input("New Session Title")
    create_session_button = st.button("Create Session")

    if create_session_button and new_session_title:
        logger.info(f"Creating session with title: {new_session_title}")

        try:
            # First check if a session with this title already exists
            existing_sessions = get_sessions()
            existing_session = None

            for session in existing_sessions:
                if session["title"].lower() == new_session_title.lower():
                    existing_session = session
                    break

            if existing_session:
                logger.info(f"Found existing session with title '{new_session_title}' and ID {existing_session['id']}")
                session = existing_session
            else:
                # Create a new session
                logger.info(f"Creating new session with title '{new_session_title}'")
                response = requests.post(
                    f"{API_URL}/sessions/",
                    json={"title": new_session_title},
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"API response status: {response.status_code}")
                if response.status_code == 200:
                    session = response.json()
                    logger.info(f"Created new session: {new_session_title} (ID: {session['id']})")
                else:
                    logger.error(f"Failed to create session: {response.status_code} - {response.text}")
                    st.error(f"Failed to create session: {response.status_code} - {response.text}")
                    session = None

            # Check if we have a valid session
            if session is not None:
                # Update session state
                st.session_state.current_session_id = session["id"]

                # Load session data
                progress_items = get_progress_items(session["id"])
                st.session_state.progress_items = progress_items

                browser_snippets = get_snippets(session["id"])
                st.session_state.browser_snippets = browser_snippets

                # Show success message
                st.success(f"Created/loaded session: {new_session_title} (ID: {session['id']})")

                # Force a rerun to update the UI
                st.experimental_rerun()
            else:
                st.error("Failed to create or find session. Please try again.")
        except Exception as e:
            logger.error(f"Error creating/loading session: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            st.error(f"Error creating/loading session: {str(e)}")

    # List and select sessions
    st.subheader("Select Session")
    sessions = get_sessions()
    session_titles = {session["id"]: session["title"] for session in sessions}

    if sessions:
        selected_session_id = st.selectbox(
            "Sessions",
            options=list(session_titles.keys()),
            format_func=lambda x: session_titles.get(x, ""),
            index=0 if st.session_state.current_session_id is None else
                  list(session_titles.keys()).index(st.session_state.current_session_id)
                  if st.session_state.current_session_id in session_titles else 0
        )

        if st.button("Load Session"):
            if selected_session_id != st.session_state.current_session_id:
                # Update session state
                st.session_state.current_session_id = selected_session_id

                # Fetch data
                progress_items = get_progress_items(selected_session_id)
                browser_snippets = get_snippets(selected_session_id)

                # Update session state with the fetched data
                st.session_state.progress_items = progress_items
                st.session_state.browser_snippets = browser_snippets

                # Reset research content when switching sessions
                st.session_state.research_content = ""

                # Add a success message
                st.success(f"Loaded session: {session_titles.get(selected_session_id, '')}")

                # Force a rerun to update the UI
                st.experimental_rerun()
            else:
                st.info(f"Session '{session_titles.get(selected_session_id, '')}' is already loaded.")

        if st.button("Delete Session"):
            response = requests.delete(f"{API_URL}/sessions/{selected_session_id}")
            if response.status_code == 200:
                st.success("Session deleted successfully!")
                # Reset current session if we deleted the active one
                if st.session_state.current_session_id == selected_session_id:
                    st.session_state.current_session_id = None
                    st.session_state.progress_items = []
                    st.session_state.browser_snippets = []
                st.experimental_rerun()

# Main content area
logger.info(f"Current session ID: {st.session_state.current_session_id}")

if st.session_state.current_session_id is not None:
    # Ensure session data is loaded
    logger.info("Ensuring session data is loaded")
    session_loaded = ensure_session_loaded(st.session_state.current_session_id)
    logger.info(f"Session loaded: {session_loaded}")

    # Get all sessions and find the current one
    sessions = get_sessions()
    logger.info(f"Retrieved {len(sessions)} sessions")

    current_session = next((s for s in sessions if s["id"] == st.session_state.current_session_id), None)
    logger.info(f"Current session found: {current_session is not None}")

    if current_session:
        st.header(f"Session: {current_session['title']}")

        # Research tabs
        tab1, tab2, tab3, tab4 = st.tabs(["Research", "History", "Browser Snippets", "Analysis Results"])

        with tab1:
            st.subheader("Research Query")
            research_prompt = st.text_area("Enter your research query", height=100)

            col1, col2 = st.columns(2)
            with col1:
                start_research_button = st.button("Start Research", disabled=st.session_state.streaming, key="start_research_button")

            # Create a placeholder for the research results
            results_container = st.container()

            # Handle research button click
            if start_research_button and research_prompt:
                with results_container:
                    st.subheader("Research Results")
                    # Create a placeholder for the streaming content
                    research_placeholder = st.empty()

                    # Reset redirection point when starting new research
                    st.session_state.redirection_point = 0

                    # Start streaming research
                    stream_research(
                        prompt=research_prompt,
                        placeholder=research_placeholder
                    )

            with col2:
                if st.button("Save Progress", disabled=st.session_state.streaming):
                    if st.session_state.research_content:
                        progress = create_progress(
                            st.session_state.current_session_id,
                            st.session_state.research_content
                        )
                        if progress:
                            st.success("Progress saved successfully!")
                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

            # Only show the research results header if we're not streaming
            if not st.session_state.streaming and st.session_state.research_content:
                st.subheader("Research Results")
                st.markdown(st.session_state.research_content)

            # Enhanced Redirection and Editing Mechanism
            st.subheader("Content Management & Redirection")

            # Text editing mode toggle
            edit_col1, edit_col2 = st.columns(2)
            with edit_col1:
                edit_mode = st.checkbox("Enable Text Editing Mode", value=st.session_state.edit_mode)
                st.session_state.edit_mode = edit_mode

            with edit_col2:
                if st.button("Reset to Original"):
                    st.session_state.edited_content = st.session_state.research_content
                    st.session_state.redirection_points = []
                    st.experimental_rerun()

            if st.session_state.research_content:
                # Text editing section
                if st.session_state.edit_mode:
                    st.markdown("### Text Editor")

                    # Initialize edited content if not set
                    if not st.session_state.edited_content:
                        st.session_state.edited_content = st.session_state.research_content

                    # Text editor with basic formatting options
                    editor_col1, editor_col2 = st.columns([3, 1])

                    with editor_col1:
                        edited_text = st.text_area(
                            "Edit your content:",
                            value=st.session_state.edited_content,
                            height=300,
                            help="You can manually edit the text here. Use the buttons on the right for formatting."
                        )
                        st.session_state.edited_content = edited_text

                    with editor_col2:
                        st.markdown("**Formatting Tools:**")

                        if st.button("Make Bold"):
                            # Simple bold formatting for selected text
                            st.session_state.edited_content += "\n\n**[Bold text here]**"
                            st.experimental_rerun()

                        if st.button("Add Heading"):
                            st.session_state.edited_content += "\n\n## New Heading"
                            st.experimental_rerun()

                        if st.button("Add Bullet Point"):
                            st.session_state.edited_content += "\n- New bullet point"
                            st.experimental_rerun()

                        if st.button("Add Line Break"):
                            st.session_state.edited_content += "\n\n---\n\n"
                            st.experimental_rerun()

                        if st.button("Save Edits"):
                            st.session_state.research_content = st.session_state.edited_content
                            st.success("Edits saved!")
                            st.experimental_rerun()

                        if st.button("Send to LLM"):
                            # Send edited content as prompt to LLM
                            if st.session_state.edited_content:
                                with st.spinner("Sending to LLM..."):
                                    llm_response = send_text_to_llm(st.session_state.edited_content)
                                    if llm_response:
                                        # Create new progress entry with LLM response
                                        create_progress(st.session_state.current_session_id, llm_response)
                                        st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)
                                        st.success("Text sent to LLM and response saved!")

                                        # Show the response
                                        st.markdown("**LLM Response:**")
                                        st.markdown(llm_response)
                                    else:
                                        st.error("Failed to get response from LLM")
                            else:
                                st.warning("No edited content to send")

                # Multiple redirection points management
                st.markdown("### Multiple Redirection Points")

                # Display current redirection points
                if st.session_state.redirection_points:
                    st.markdown("**Current Redirection Points:**")
                    for i, point in enumerate(st.session_state.redirection_points):
                        col1, col2, col3 = st.columns([2, 2, 1])
                        with col1:
                            st.write(f"Point {i+1}: Position {point['position']}")
                        with col2:
                            st.write(f"Instruction: {point['instruction'][:50]}...")
                        with col3:
                            if st.button(f"Remove", key=f"remove_point_{i}"):
                                st.session_state.redirection_points.pop(i)
                                st.experimental_rerun()

                # Add new redirection point
                red_col1, red_col2 = st.columns([2, 1])

                with red_col1:
                    # Show current content with markers for all redirection points
                    display_content = st.session_state.edited_content if st.session_state.edit_mode else st.session_state.research_content

                    if st.session_state.redirection_points:
                        marked_content = highlight_text_with_redirection_points(display_content, st.session_state.redirection_points)
                        st.markdown("**Content with Redirection Points:**")
                        st.markdown(marked_content)
                    else:
                        st.markdown("**Current Content:**")
                        st.markdown(display_content)

                with red_col2:
                    # Single redirection point selector (for adding new points)
                    content_to_use = st.session_state.edited_content if st.session_state.edit_mode else st.session_state.research_content
                    redirection_point = st.slider(
                        "Select new redirection point",
                        0, len(content_to_use),
                        st.session_state.redirection_point if "redirection_point" in st.session_state else 0
                    )
                    st.session_state.redirection_point = redirection_point

                    # Preview the text up to the redirection point
                    st.text_area(
                        "Text up to redirection point",
                        content_to_use[:redirection_point],
                        height=100
                    )

                    # Text selection for redirection (alternative to slider)
                    st.markdown("**Or select text manually:**")
                    selected_text = st.text_input(
                        "Paste selected text here",
                        help="Copy and paste the text where you want to add a redirection point"
                    )

                    if selected_text and st.button("Find Text Position"):
                        position = content_to_use.find(selected_text)
                        if position != -1:
                            st.session_state.redirection_point = position
                            st.success(f"Found text at position {position}")
                            st.experimental_rerun()
                        else:
                            st.error("Text not found in content")

                # New instruction for redirection
                new_instruction = st.text_area("New instruction for redirection",
                                              placeholder="Enter instructions to continue from the redirection point...",
                                              height=100)

                # Add redirection point button
                if st.button("Add Redirection Point") and new_instruction:
                    if redirection_point > 0:
                        new_point = {
                            "position": redirection_point,
                            "instruction": new_instruction,
                            "timestamp": datetime.now().isoformat()
                        }
                        st.session_state.redirection_points.append(new_point)
                        st.success(f"Added redirection point at position {redirection_point}")
                        st.experimental_rerun()
                    else:
                        st.warning("Please set a redirection point greater than 0")

                # Select a browser snippet for context (if any)
                snippet_options = {0: "None"}
                for snippet in st.session_state.browser_snippets:
                    snippet_options[snippet["id"]] = f"{snippet['content'][:50]}..."

                selected_snippet_id = st.selectbox(
                    "Select browser snippet for context",
                    options=list(snippet_options.keys()),
                    format_func=lambda x: snippet_options.get(x, "")
                )

                # Redirection application options
                st.markdown("### Apply Redirections")

                redirect_col1, redirect_col2 = st.columns(2)

                with redirect_col1:
                    # Apply single redirection (original functionality)
                    apply_single_redirection = st.button("Apply Single Redirection",
                                                       disabled=st.session_state.streaming,
                                                       key="apply_single_redirection")

                with redirect_col2:
                    # Apply multiple redirections
                    apply_multiple_redirections = st.button("Apply All Redirection Points",
                                                          disabled=st.session_state.streaming or not st.session_state.redirection_points,
                                                          key="apply_multiple_redirections")

                # Create a container for redirection results
                redirection_results = st.container()

                # Handle single redirection (original functionality)
                if apply_single_redirection:
                    if new_instruction and redirection_point > 0:
                        with redirection_results:
                            st.subheader("Single Redirection Results")
                            redirect_placeholder = st.empty()

                            # Prepare context data if a snippet is selected
                            context = None
                            if selected_snippet_id != 0:
                                snippet = next((s for s in st.session_state.browser_snippets if s["id"] == selected_snippet_id), None)
                                if snippet:
                                    context = [snippet["content"]]

                            # Create a new prompt that includes the redirection
                            content_to_redirect = st.session_state.edited_content if st.session_state.edit_mode else st.session_state.research_content
                            new_prompt = f"{content_to_redirect[:redirection_point]}\n\nContinue from here: {new_instruction}"

                            # If we have context from a snippet, add it
                            if context:
                                new_prompt += f"\n\nContext: {context[0]}"

                            # Stream the redirected content
                            stream_research(
                                prompt=new_prompt,
                                placeholder=redirect_placeholder
                            )

                            # Update progress items after streaming
                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)
                    else:
                        st.warning("Please provide a redirection instruction and set a redirection point.")

                # Handle multiple redirections
                if apply_multiple_redirections:
                    with redirection_results:
                        st.subheader("Multiple Redirections Results")

                        # Apply all redirection points
                        content_to_redirect = st.session_state.edited_content if st.session_state.edit_mode else st.session_state.research_content

                        with st.spinner("Applying multiple redirections..."):
                            redirected_content = apply_multiple_redirections(
                                content_to_redirect,
                                st.session_state.redirection_points,
                                st.session_state.current_session_id
                            )

                            # Update the research content
                            st.session_state.research_content = redirected_content
                            if st.session_state.edit_mode:
                                st.session_state.edited_content = redirected_content

                            # Save to progress
                            create_progress(st.session_state.current_session_id, redirected_content)
                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

                            st.success("Multiple redirections applied successfully!")
                            st.markdown("**Updated Content:**")
                            st.markdown(redirected_content)

        with tab2:
            st.subheader("Research History")

            if st.session_state.progress_items:
                for i, progress in enumerate(st.session_state.progress_items):
                    with st.expander(f"Progress {i+1} - {progress['timestamp']}"):
                        st.markdown(progress["content"])

                        if st.button(f"Load this progress", key=f"load_progress_{i}"):
                            st.session_state.research_content = progress["content"]
                            st.experimental_rerun()

                        if st.button(f"Delete this progress", key=f"delete_progress_{i}"):
                            response = requests.delete(f"{API_URL}/progress/{progress['id']}")
                            if response.status_code == 200:
                                st.success("Progress deleted successfully!")
                                st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)
                                st.experimental_rerun()
            else:
                st.info("No research history available for this session.")

        with tab3:
            st.subheader("Browser Snippets")

            # Add new browser snippet
            st.markdown("Add a new browser snippet:")
            snippet_content = st.text_area("Snippet Content", height=100)
            snippet_url = st.text_input("Source URL (optional)")

            if st.button("Add Snippet"):
                if snippet_content:
                    snippet = create_snippet(
                        st.session_state.current_session_id,
                        snippet_content,
                        snippet_url if snippet_url else None
                    )

                    if snippet:
                        st.success("Snippet added successfully!")
                        st.session_state.browser_snippets = get_snippets(st.session_state.current_session_id)
                        st.experimental_rerun()

            # Display existing snippets
            st.markdown("Existing Snippets:")
            if st.session_state.browser_snippets:
                for i, snippet in enumerate(st.session_state.browser_snippets):
                    with st.expander(f"Snippet {i+1} - {snippet['timestamp']}"):
                        st.markdown(snippet["content"])
                        if snippet["source_url"]:
                            st.markdown(f"Source: {snippet['source_url']}")

                        if st.button(f"Delete this snippet", key=f"delete_snippet_{i}"):
                            response = requests.delete(f"{API_URL}/snippets/{snippet['id']}")
                            if response.status_code == 200:
                                st.success("Snippet deleted successfully!")
                                st.session_state.browser_snippets = get_snippets(st.session_state.current_session_id)
                                st.experimental_rerun()
            else:
                st.info("No browser snippets available for this session.")

        with tab4:
            st.subheader("Analysis Results")

            # Filter snippets that are analysis results (have analysis:// source_url)
            analysis_snippets = [
                snippet for snippet in st.session_state.browser_snippets
                if snippet.get("source_url", "").startswith("analysis://")
            ]

            if analysis_snippets:
                st.markdown("**Analysis Results from Browser Extension:**")

                for i, snippet in enumerate(analysis_snippets):
                    analysis_type = snippet["source_url"].replace("analysis://", "").replace("_", " ").title()

                    with st.expander(f"Analysis {i+1}: {analysis_type} - {snippet['timestamp']}"):
                        st.markdown(snippet["content"])

                        col1, col2, col3 = st.columns(3)

                        with col1:
                            if st.button(f"Use as Redirection", key=f"redirect_analysis_{i}"):
                                # Use analysis result as redirection instruction
                                if st.session_state.research_content:
                                    analysis_instruction = f"Based on this analysis: {snippet['content']}\n\nContinue the research with these insights."

                                    with st.spinner("Applying analysis-based redirection..."):
                                        llm_response = send_text_to_llm(analysis_instruction)
                                        if llm_response:
                                            # Concatenate with existing content
                                            combined_content = st.session_state.research_content + "\n\n## Analysis-Based Continuation\n\n" + llm_response
                                            st.session_state.research_content = combined_content

                                            # Save to progress
                                            create_progress(st.session_state.current_session_id, combined_content)
                                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

                                            st.success("Analysis applied as redirection and concatenated with existing content!")
                                            st.experimental_rerun()
                                        else:
                                            st.error("Failed to apply analysis as redirection")
                                else:
                                    st.warning("No research content available to redirect")

                        with col2:
                            if st.button(f"Generate Code", key=f"generate_code_{i}"):
                                # Generate code based on analysis
                                code_prompt = f"Generate Python code to perform this analysis: {analysis_type}\n\nBased on this example result:\n{snippet['content']}\n\nProvide clean, executable Python code."

                                with st.spinner("Generating code..."):
                                    code_response = send_text_to_llm(code_prompt)
                                    if code_response:
                                        st.markdown("**Generated Code:**")
                                        st.code(code_response, language="python")

                                        # Option to execute the code
                                        if st.button(f"Execute Code", key=f"execute_code_{i}"):
                                            if st.session_state.research_content:
                                                execution_result = execute_analysis_code(code_response, st.session_state.research_content)
                                                st.markdown("**Execution Result:**")
                                                st.text(execution_result)
                                            else:
                                                st.warning("No research content to analyze")
                                    else:
                                        st.error("Failed to generate code")

                        with col3:
                            if st.button(f"Delete Analysis", key=f"delete_analysis_{i}"):
                                response = requests.delete(f"{API_URL}/snippets/{snippet['id']}")
                                if response.status_code == 200:
                                    st.success("Analysis deleted successfully!")
                                    st.session_state.browser_snippets = get_snippets(st.session_state.current_session_id)
                                    st.experimental_rerun()

                # Bulk operations
                st.markdown("### Bulk Operations")
                bulk_col1, bulk_col2 = st.columns(2)

                with bulk_col1:
                    if st.button("Combine All Analyses"):
                        # Combine all analysis results into a comprehensive report
                        combined_analysis = "# Comprehensive Analysis Report\n\n"
                        for snippet in analysis_snippets:
                            analysis_type = snippet["source_url"].replace("analysis://", "").replace("_", " ").title()
                            combined_analysis += f"## {analysis_type}\n\n{snippet['content']}\n\n---\n\n"

                        # Send combined analysis to LLM for synthesis
                        synthesis_prompt = f"Synthesize these analysis results into a comprehensive insight:\n\n{combined_analysis}"

                        with st.spinner("Synthesizing analyses..."):
                            synthesis_response = send_text_to_llm(synthesis_prompt)
                            if synthesis_response:
                                # Concatenate with existing content
                                if st.session_state.research_content:
                                    final_content = st.session_state.research_content + "\n\n## Comprehensive Analysis Synthesis\n\n" + synthesis_response
                                else:
                                    final_content = "## Comprehensive Analysis Synthesis\n\n" + synthesis_response

                                st.session_state.research_content = final_content
                                create_progress(st.session_state.current_session_id, final_content)
                                st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

                                st.success("All analyses combined and synthesized!")
                                st.experimental_rerun()
                            else:
                                st.error("Failed to synthesize analyses")

                with bulk_col2:
                    if st.button("Clear All Analyses"):
                        # Delete all analysis snippets
                        deleted_count = 0
                        for snippet in analysis_snippets:
                            response = requests.delete(f"{API_URL}/snippets/{snippet['id']}")
                            if response.status_code == 200:
                                deleted_count += 1

                        if deleted_count > 0:
                            st.success(f"Deleted {deleted_count} analysis results!")
                            st.session_state.browser_snippets = get_snippets(st.session_state.current_session_id)
                            st.experimental_rerun()
            else:
                st.info("No analysis results available. Use the browser extension to analyze text on web pages.")

                # Instructions for using analysis features
                st.markdown("""
                ### How to Use Analysis Features:

                1. **Browser Extension Analysis:**
                   - Select text on any webpage
                   - Right-click and choose "Analyze Text"
                   - Select from: Sentiment Analysis, Word Count, Readability, or Keywords
                   - Results will appear here automatically

                2. **Use Analysis for Redirection:**
                   - Click "Use as Redirection" to apply analysis insights to your research
                   - The LLM will continue your research based on the analysis

                3. **Generate and Execute Code:**
                   - Click "Generate Code" to create Python code for the analysis
                   - Execute the code on your current research content

                4. **Combine Multiple Analyses:**
                   - Use "Combine All Analyses" to synthesize multiple analysis results
                   - Creates a comprehensive insight from all your analyses
                """)
else:
    st.info("Please create or select a research session from the sidebar to get started.")

# Debug statements have been removed as they were causing issues