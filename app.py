import streamlit as st
import requests
import logging
import time
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("app")

# API URL
API_URL = "http://localhost:8000"

# Page configuration
st.set_page_config(page_title="Deep Research Assistant", layout="wide")

# Initialize session state variables
if "current_session_id" not in st.session_state:
    st.session_state.current_session_id = None
if "research_content" not in st.session_state:
    st.session_state.research_content = ""
if "streaming" not in st.session_state:
    st.session_state.streaming = False
if "progress_items" not in st.session_state:
    st.session_state.progress_items = []
if "browser_snippets" not in st.session_state:
    st.session_state.browser_snippets = []
if "selected_progress" not in st.session_state:
    st.session_state.selected_progress = None
if "redirection_point" not in st.session_state:
    st.session_state.redirection_point = 0

# Helper functions
def get_sessions() -> List[Dict[str, Any]]:
    """Get all research sessions from the API"""
    try:
        logger.info(f"Fetching sessions from {API_URL}/sessions/")
        response = requests.get(f"{API_URL}/sessions/")
        logger.info(f"Sessions API response status: {response.status_code}")

        if response.status_code == 200:
            sessions = response.json()
            logger.info(f"Retrieved {len(sessions)} sessions: {sessions}")
            return sessions

        logger.error(f"Failed to get sessions: {response.status_code} - {response.text}")
        return []
    except Exception as e:
        logger.error(f"Error getting sessions: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def create_session(title: str) -> Optional[Dict[str, Any]]:
    """Create a new research session"""
    try:
        # First check if a session with this title already exists
        logger.info(f"Checking if session with title '{title}' already exists")
        existing_sessions = get_sessions()
        logger.info(f"Found {len(existing_sessions)} existing sessions")

        for session in existing_sessions:
            if session["title"].lower() == title.lower():
                logger.info(f"Session with title '{title}' already exists, returning existing session with ID {session['id']}")
                return session  # Return existing session instead of creating a new one

        # If no existing session found, create a new one
        logger.info(f"Creating new session with title '{title}'")
        response = requests.post(f"{API_URL}/sessions/", json={"title": title})

        logger.info(f"API response status: {response.status_code}")
        if response.status_code == 200:
            session_data = response.json()
            logger.info(f"Created new session: {title} (ID: {session_data['id']})")
            return session_data

        logger.error(f"Failed to create session: {response.status_code} - {response.text}")
        return None
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def get_progress_items(session_id: int) -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/progress/session/{session_id}")
    if response.status_code == 200:
        return response.json()
    return []

def create_progress(session_id: int, content: str) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/progress/", json={
        "session_id": session_id,
        "content": content
    })
    if response.status_code == 200:
        return response.json()
    return None

def get_snippets(session_id: int) -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/snippets/session/{session_id}")
    if response.status_code == 200:
        return response.json()
    return []

def create_snippet(session_id: int, content: str, source_url: str = None) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/snippets/", json={
        "session_id": session_id,
        "content": content,
        "source_url": source_url
    })
    if response.status_code == 200:
        return response.json()
    return None

def create_redirection(session_id: int, progress_id: int, redirection_point: int,
                      new_instruction: str, browser_snippet_id: int = None) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/redirect/", json={
        "session_id": session_id,
        "progress_id": progress_id,
        "redirection_point": redirection_point,
        "new_instruction": new_instruction,
        "browser_snippet_id": browser_snippet_id
    })
    if response.status_code == 200:
        return response.json()
    return None

def ensure_session_loaded(session_id: int):
    """Ensure that all data for a session is properly loaded into session state"""
    if session_id is not None:
        logger.info(f"Loading session data for session ID: {session_id}")

        # Update the current session ID
        st.session_state.current_session_id = session_id
        logger.info(f"Set current_session_id to {session_id}")

        # Load progress items
        progress_items = get_progress_items(session_id)
        st.session_state.progress_items = progress_items
        logger.info(f"Loaded {len(progress_items)} progress items")

        # Load browser snippets
        browser_snippets = get_snippets(session_id)
        st.session_state.browser_snippets = browser_snippets
        logger.info(f"Loaded {len(browser_snippets)} browser snippets")

        return True

    logger.warning(f"Cannot load session data: session_id is None")
    return False

def stream_research(prompt: str, placeholder, redirection_data=None, context=None):
    """Stream research content and update the UI in real-time with support for redirection"""
    # Use a non-async function to avoid issues with Streamlit's event loop
    st.session_state.streaming = True
    st.session_state.research_content = ""

    # Create a direct output area that will always be visible
    output_area = st.empty()
    output_area.markdown("**Starting research...**")

    # Create a status indicator and a progress bar
    status = st.info("Starting research stream...")
    progress_bar = st.progress(0)

    # Prepare request data
    request_data = {
        "prompt": prompt
    }

    # Add redirection data if provided
    if redirection_data:
        request_data["redirection"] = redirection_data

    # Add context if provided
    if context:
        request_data["context"] = context

    try:
        # Make a direct request to generate content (non-streaming)
        # This is more reliable than streaming with Streamlit
        response = requests.post(
            f"{API_URL}/research/generate",
            json={"prompt": prompt},
            headers={"Content-Type": "application/json"}
        )

        if response.status_code != 200:
            st.error(f"Error: {response.status_code} - {response.text}")
            status.error("Research failed!")
            st.session_state.streaming = False
            return

        # Get the content from the response
        data = response.json()
        content = data.get("content", "")

        # Simulate streaming by showing content gradually
        total_length = len(content)
        chunk_size = max(1, total_length // 20)  # Show content in about 20 chunks

        for i in range(0, total_length, chunk_size):
            # Update progress
            progress_value = min(i / total_length, 0.99)
            progress_bar.progress(progress_value)

            # Get the next chunk
            current_content = content[:i+chunk_size]

            # Update the content display
            output_area.markdown(current_content)

            # Update the session state
            st.session_state.research_content = current_content

            # Update the placeholder
            placeholder.markdown(current_content)

            # Small delay to simulate streaming
            time.sleep(0.1)

        # Final update
        progress_bar.progress(1.0)
        status.success("Research complete!")
        output_area.markdown(content)
        st.session_state.research_content = content
        placeholder.markdown(content)

    except Exception as e:
        error_msg = f"Error in research: {str(e)}"
        st.error(error_msg)
        status.error("Research failed!")
        import traceback
        st.error(traceback.format_exc())
    finally:
        st.session_state.streaming = False

    # Save the research content to the database
    if st.session_state.current_session_id and st.session_state.research_content:
        create_progress(st.session_state.current_session_id, st.session_state.research_content)
        # Update progress items
        st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

    # Return the final content
    return st.session_state.research_content

# UI Components
st.title("Deep Research Assistant with Gemini Integration")

# Sidebar for session management
with st.sidebar:
    st.header("Research Sessions")

    # Create new session
    new_session_title = st.text_input("New Session Title")
    create_session_button = st.button("Create Session")

    if create_session_button and new_session_title:
        logger.info(f"Creating session with title: {new_session_title}")

        try:
            # First check if a session with this title already exists
            existing_sessions = get_sessions()
            existing_session = None

            for session in existing_sessions:
                if session["title"].lower() == new_session_title.lower():
                    existing_session = session
                    break

            if existing_session:
                logger.info(f"Found existing session with title '{new_session_title}' and ID {existing_session['id']}")
                session = existing_session
            else:
                # Create a new session
                logger.info(f"Creating new session with title '{new_session_title}'")
                response = requests.post(
                    f"{API_URL}/sessions/",
                    json={"title": new_session_title},
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"API response status: {response.status_code}")
                if response.status_code == 200:
                    session = response.json()
                    logger.info(f"Created new session: {new_session_title} (ID: {session['id']})")
                else:
                    logger.error(f"Failed to create session: {response.status_code} - {response.text}")
                    st.error(f"Failed to create session: {response.status_code} - {response.text}")
                    session = None

            # Check if we have a valid session
            if session is not None:
                # Update session state
                st.session_state.current_session_id = session["id"]

                # Load session data
                progress_items = get_progress_items(session["id"])
                st.session_state.progress_items = progress_items

                browser_snippets = get_snippets(session["id"])
                st.session_state.browser_snippets = browser_snippets

                # Show success message
                st.success(f"Created/loaded session: {new_session_title} (ID: {session['id']})")

                # Force a rerun to update the UI
                st.experimental_rerun()
            else:
                st.error("Failed to create or find session. Please try again.")
        except Exception as e:
            logger.error(f"Error creating/loading session: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            st.error(f"Error creating/loading session: {str(e)}")

    # List and select sessions
    st.subheader("Select Session")
    sessions = get_sessions()
    session_titles = {session["id"]: session["title"] for session in sessions}

    if sessions:
        selected_session_id = st.selectbox(
            "Sessions",
            options=list(session_titles.keys()),
            format_func=lambda x: session_titles.get(x, ""),
            index=0 if st.session_state.current_session_id is None else
                  list(session_titles.keys()).index(st.session_state.current_session_id)
                  if st.session_state.current_session_id in session_titles else 0
        )

        if st.button("Load Session"):
            if selected_session_id != st.session_state.current_session_id:
                # Update session state
                st.session_state.current_session_id = selected_session_id

                # Fetch data
                progress_items = get_progress_items(selected_session_id)
                browser_snippets = get_snippets(selected_session_id)

                # Update session state with the fetched data
                st.session_state.progress_items = progress_items
                st.session_state.browser_snippets = browser_snippets

                # Reset research content when switching sessions
                st.session_state.research_content = ""

                # Add a success message
                st.success(f"Loaded session: {session_titles.get(selected_session_id, '')}")

                # Force a rerun to update the UI
                st.experimental_rerun()
            else:
                st.info(f"Session '{session_titles.get(selected_session_id, '')}' is already loaded.")

        if st.button("Delete Session"):
            response = requests.delete(f"{API_URL}/sessions/{selected_session_id}")
            if response.status_code == 200:
                st.success("Session deleted successfully!")
                # Reset current session if we deleted the active one
                if st.session_state.current_session_id == selected_session_id:
                    st.session_state.current_session_id = None
                    st.session_state.progress_items = []
                    st.session_state.browser_snippets = []
                st.experimental_rerun()

# Main content area
logger.info(f"Current session ID: {st.session_state.current_session_id}")

if st.session_state.current_session_id is not None:
    # Ensure session data is loaded
    logger.info("Ensuring session data is loaded")
    session_loaded = ensure_session_loaded(st.session_state.current_session_id)
    logger.info(f"Session loaded: {session_loaded}")

    # Get all sessions and find the current one
    sessions = get_sessions()
    logger.info(f"Retrieved {len(sessions)} sessions")

    current_session = next((s for s in sessions if s["id"] == st.session_state.current_session_id), None)
    logger.info(f"Current session found: {current_session is not None}")

    if current_session:
        st.header(f"Session: {current_session['title']}")

        # Research tabs
        tab1, tab2, tab3 = st.tabs(["Research", "History", "Browser Snippets"])

        with tab1:
            st.subheader("Research Query")
            research_prompt = st.text_area("Enter your research query", height=100)

            col1, col2 = st.columns(2)
            with col1:
                start_research_button = st.button("Start Research", disabled=st.session_state.streaming, key="start_research_button")

            # Create a placeholder for the research results
            results_container = st.container()

            # Handle research button click
            if start_research_button and research_prompt:
                with results_container:
                    st.subheader("Research Results")
                    # Create a placeholder for the streaming content
                    research_placeholder = st.empty()

                    # Reset redirection point when starting new research
                    st.session_state.redirection_point = 0

                    # Start streaming research
                    stream_research(
                        prompt=research_prompt,
                        placeholder=research_placeholder
                    )

            with col2:
                if st.button("Save Progress", disabled=st.session_state.streaming):
                    if st.session_state.research_content:
                        progress = create_progress(
                            st.session_state.current_session_id,
                            st.session_state.research_content
                        )
                        if progress:
                            st.success("Progress saved successfully!")
                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

            # Only show the research results header if we're not streaming
            if not st.session_state.streaming and st.session_state.research_content:
                st.subheader("Research Results")
                st.markdown(st.session_state.research_content)

            # Redirection mechanism
            st.subheader("Redirection Mechanism")

            # Select a point in the text for redirection
            if st.session_state.research_content:
                # Create columns for better layout
                red_col1, red_col2 = st.columns([2, 1])

                with red_col1:
                    # Show current content with a marker at the redirection point
                    if "redirection_point" in st.session_state and st.session_state.redirection_point > 0:
                        content_before = st.session_state.research_content[:st.session_state.redirection_point]
                        content_after = st.session_state.research_content[st.session_state.redirection_point:]
                        st.markdown("**Current Content with Redirection Point:**")
                        st.markdown(f"{content_before}**[REDIRECTION POINT]**{content_after}")
                    else:
                        st.markdown("**Current Content:**")
                        st.markdown(st.session_state.research_content)

                with red_col2:
                    redirection_point = st.slider(
                        "Select redirection point (character index)",
                        0, len(st.session_state.research_content),
                        st.session_state.redirection_point if "redirection_point" in st.session_state else 0
                    )
                    st.session_state.redirection_point = redirection_point

                    # Preview the text up to the redirection point
                    st.text_area(
                        "Text up to redirection point",
                        st.session_state.research_content[:redirection_point],
                        height=100
                    )

                # New instruction for redirection
                new_instruction = st.text_area("New instruction for redirection",
                                              placeholder="Enter instructions to continue from the redirection point...",
                                              height=100)

                # Select a browser snippet for context (if any)
                snippet_options = {0: "None"}
                for snippet in st.session_state.browser_snippets:
                    snippet_options[snippet["id"]] = f"{snippet['content'][:50]}..."

                selected_snippet_id = st.selectbox(
                    "Select browser snippet for context",
                    options=list(snippet_options.keys()),
                    format_func=lambda x: snippet_options.get(x, "")
                )

                # Apply redirection button
                apply_redirection_button = st.button("Apply Redirection",
                                                   disabled=st.session_state.streaming,
                                                   key="apply_redirection_button")

                # Create a container for redirection results
                redirection_results = st.container()

                if apply_redirection_button:
                    if new_instruction and redirection_point > 0:
                        with redirection_results:
                            st.subheader("Redirection Results")
                            # Create a placeholder for the streaming content
                            redirect_placeholder = st.empty()

                            # Prepare redirection data
                            redirection_data = {
                                "redirection_point": redirection_point,
                                "new_instruction": new_instruction,
                                "original_content": st.session_state.research_content
                            }

                            # Prepare context data if a snippet is selected
                            context = None
                            if selected_snippet_id != 0:
                                snippet = next((s for s in st.session_state.browser_snippets if s["id"] == selected_snippet_id), None)
                                if snippet:
                                    context = [snippet["content"]]

                            # Use the redirection mechanism
                            # Create a new prompt that includes the redirection
                            new_prompt = f"{st.session_state.research_content[:redirection_point]}\n\nContinue from here: {new_instruction}"

                            # If we have context from a snippet, add it
                            if context:
                                new_prompt += f"\n\nContext: {context[0]}"

                            # Stream the redirected content
                            stream_research(
                                prompt=new_prompt,
                                placeholder=redirect_placeholder
                            )

                            # Update progress items after streaming
                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)
                    else:
                        st.warning("Please provide a redirection instruction and set a redirection point.")

        with tab2:
            st.subheader("Research History")

            if st.session_state.progress_items:
                for i, progress in enumerate(st.session_state.progress_items):
                    with st.expander(f"Progress {i+1} - {progress['timestamp']}"):
                        st.markdown(progress["content"])

                        if st.button(f"Load this progress", key=f"load_progress_{i}"):
                            st.session_state.research_content = progress["content"]
                            st.experimental_rerun()

                        if st.button(f"Delete this progress", key=f"delete_progress_{i}"):
                            response = requests.delete(f"{API_URL}/progress/{progress['id']}")
                            if response.status_code == 200:
                                st.success("Progress deleted successfully!")
                                st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)
                                st.experimental_rerun()
            else:
                st.info("No research history available for this session.")

        with tab3:
            st.subheader("Browser Snippets")

            # Add new browser snippet
            st.markdown("Add a new browser snippet:")
            snippet_content = st.text_area("Snippet Content", height=100)
            snippet_url = st.text_input("Source URL (optional)")

            if st.button("Add Snippet"):
                if snippet_content:
                    snippet = create_snippet(
                        st.session_state.current_session_id,
                        snippet_content,
                        snippet_url if snippet_url else None
                    )

                    if snippet:
                        st.success("Snippet added successfully!")
                        st.session_state.browser_snippets = get_snippets(st.session_state.current_session_id)
                        st.experimental_rerun()

            # Display existing snippets
            st.markdown("Existing Snippets:")
            if st.session_state.browser_snippets:
                for i, snippet in enumerate(st.session_state.browser_snippets):
                    with st.expander(f"Snippet {i+1} - {snippet['timestamp']}"):
                        st.markdown(snippet["content"])
                        if snippet["source_url"]:
                            st.markdown(f"Source: {snippet['source_url']}")

                        if st.button(f"Delete this snippet", key=f"delete_snippet_{i}"):
                            response = requests.delete(f"{API_URL}/snippets/{snippet['id']}")
                            if response.status_code == 200:
                                st.success("Snippet deleted successfully!")
                                st.session_state.browser_snippets = get_snippets(st.session_state.current_session_id)
                                st.experimental_rerun()
            else:
                st.info("No browser snippets available for this session.")
else:
    st.info("Please create or select a research session from the sidebar to get started.")

# Debug statements have been removed as they were causing issues