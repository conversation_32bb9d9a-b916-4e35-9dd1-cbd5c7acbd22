import os
import json
from datetime import datetime
import uuid

class ResearchManager:
    def __init__(self, storage_dir="research_data"):
        """Initialize the research manager with storage directory"""
        self.storage_dir = storage_dir
        
        # Create storage directory if it doesn't exist
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir)
    
    def create_research(self, title):
        """Create a new research entry"""
        research_id = str(uuid.uuid4())
        research = {
            "id": research_id,
            "title": title,
            "content": "",
            "context": [],
            "history": [],
            "created_at": self.get_timestamp(),
            "updated_at": self.get_timestamp()
        }
        
        # Save to file
        self._save_research(research)
        
        return research
    
    def get_research(self, research_id):
        """Retrieve a research entry by ID"""
        file_path = os.path.join(self.storage_dir, f"{research_id}.json")
        
        if os.path.exists(file_path):
            with open(file_path, 'r') as file:
                return json.load(file)
        return None
    
    def get_all_research(self):
        """Get all research entries"""
        research_list = []
        
        # List all JSON files in the storage directory
        if os.path.exists(self.storage_dir):
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.storage_dir, filename)
                    with open(file_path, 'r') as file:
                        research_list.append(json.load(file))
        
        return research_list
    
    def update_research(self, research):
        """Update an existing research entry"""
        if 'id' not in research:
            raise ValueError("Research ID is required for update")
        
        # Update timestamp
        research["updated_at"] = self.get_timestamp()
        
        # Save to file
        self._save_research(research)
        
        return research
    
    def delete_research(self, research_id):
        """Delete a research entry by ID"""
        file_path = os.path.join(self.storage_dir, f"{research_id}.json")
        
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    
    def _save_research(self, research):
        """Save research to file"""
        file_path = os.path.join(self.storage_dir, f"{research['id']}.json")
        
        with open(file_path, 'w') as file:
            json.dump(research, file, indent=2)
    
    def get_timestamp(self):
        """Get current timestamp in string format"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")