import streamlit as st
import requests
import json
import time
from datetime import datetime
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional

# API URL
API_URL = "http://localhost:8000"

# Page configuration
st.set_page_config(page_title="Deep Research Assistant", layout="wide")

# Initialize session state variables
if "current_session_id" not in st.session_state:
    st.session_state.current_session_id = None
if "research_content" not in st.session_state:
    st.session_state.research_content = ""
if "streaming" not in st.session_state:
    st.session_state.streaming = False
if "progress_items" not in st.session_state:
    st.session_state.progress_items = []
if "browser_snippets" not in st.session_state:
    st.session_state.browser_snippets = []
if "selected_progress" not in st.session_state:
    st.session_state.selected_progress = None
if "redirection_point" not in st.session_state:
    st.session_state.redirection_point = 0

# Helper functions
def get_sessions() -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/sessions/")
    if response.status_code == 200:
        return response.json()
    return []

def create_session(title: str) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/sessions/", json={"title": title})
    if response.status_code == 200:
        return response.json()
    return None

def get_progress_items(session_id: int) -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/progress/session/{session_id}")
    if response.status_code == 200:
        return response.json()
    return []

def create_progress(session_id: int, content: str) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/progress/", json={
        "session_id": session_id,
        "content": content
    })
    if response.status_code == 200:
        return response.json()
    return None

def get_snippets(session_id: int) -> List[Dict[str, Any]]:
    response = requests.get(f"{API_URL}/snippets/session/{session_id}")
    if response.status_code == 200:
        return response.json()
    return []

def create_snippet(session_id: int, content: str, source_url: str = None) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/snippets/", json={
        "session_id": session_id,
        "content": content,
        "source_url": source_url
    })
    if response.status_code == 200:
        return response.json()
    return None

def create_redirection(session_id: int, progress_id: int, redirection_point: int, 
                      new_instruction: str, browser_snippet_id: int = None) -> Optional[Dict[str, Any]]:
    response = requests.post(f"{API_URL}/redirect/", json={
        "session_id": session_id,
        "progress_id": progress_id,
        "redirection_point": redirection_point,
        "new_instruction": new_instruction,
        "browser_snippet_id": browser_snippet_id
    })
    if response.status_code == 200:
        return response.json()
    return None

async def stream_research(prompt: str):
    st.session_state.streaming = True
    st.session_state.research_content = ""
    
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{API_URL}/research/stream",
            json={"prompt": prompt},
            headers={"Content-Type": "application/json"}
        ) as response:
            if response.status != 200:
                st.error(f"Error: {response.status}")
                st.session_state.streaming = False
                return
            
            # Process the server-sent events
            buffer = ""
            async for line in response.content:
                line = line.decode('utf-8')
                buffer += line
                
                if buffer.endswith("\n\n"):
                    if buffer.startswith("data: "):
                        data = json.loads(buffer[6:].strip())
                        st.session_state.research_content += data.get("content", "")
                        # Force a rerun to update the UI
                        st.experimental_rerun()
                    buffer = ""
    
    st.session_state.streaming = False
    
    # Save the research content to the database
    if st.session_state.current_session_id and st.session_state.research_content:
        create_progress(st.session_state.current_session_id, st.session_state.research_content)
        # Update progress items
        st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)

# UI Components
st.title("Deep Research Assistant with Gemini Integration")

# Sidebar for session management
with st.sidebar:
    st.header("Research Sessions")
    
    # Create new session
    new_session_title = st.text_input("New Session Title")
    if st.button("Create Session") and new_session_title:
        session = create_session(new_session_title)
        if session:
            st.session_state.current_session_id = session["id"]
            st.success(f"Created session: {new_session_title}")
            st.experimental_rerun()
    
    # List and select sessions
    st.subheader("Select Session")
    sessions = get_sessions()
    session_titles = {session["id"]: session["title"] for session in sessions}
    
    if sessions:
        selected_session_id = st.selectbox(
            "Sessions",
            options=list(session_titles.keys()),
            format_func=lambda x: session_titles.get(x, ""),
            index=0 if st.session_state.current_session_id is None else 
                  list(session_titles.keys()).index(st.session_state.current_session_id) 
                  if st.session_state.current_session_id in session_titles else 0
        )
        
        if st.button("Load Session"):
            st.session_state.current_session_id = selected_session_id
            st.session_state.progress_items = get_progress_items(selected_session_id)
            st.session_state.browser_snippets = get_snippets(selected_session_id)
            st.experimental_rerun()

# Main content area
if st.session_state.current_session_id is not None:
    current_session = next((s for s in sessions if s["id"] == st.session_state.current_session_id), None)
    
    if current_session:
        st.header(f"Session: {current_session['title']}")
        
        # Research tabs
        tab1, tab2, tab3 = st.tabs(["Research", "History", "Browser Snippets"])
        
        with tab1:
            st.subheader("Research Query")
            research_prompt = st.text_area("Enter your research query", height=100)
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("Start Research", disabled=st.session_state.streaming):
                    if research_prompt:
                        asyncio.run(stream_research(research_prompt))
            
            with col2:
                if st.button("Save Progress", disabled=st.session_state.streaming):
                    if st.session_state.research_content:
                        progress = create_progress(
                            st.session_state.current_session_id, 
                            st.session_state.research_content
                        )
                        if progress:
                            st.success("Progress saved successfully!")
                            st.session_state.progress_items = get_progress_items(st.session_state.current_session_id)
            
            st.subheader("Research Results")
            research_container = st.container()
            with research_container:
                st.markdown(st.session_state.research_content)
            
            # Redirection mechanism
            st.subheader("Redirection Mechanism")
            
            # Select a point in the text for redirection
            if st.session_state.research_content:
                redirection_point = st.slider(
                    "Select redirection point (character index)", 
                    0, len(st.session_state.research_content), 
                    st.session_state.redirection_point
                )
                st.session_state.redirection_point = redirection_point
                
                # Preview the text up to the redirection point
                st.text_area(
                    "Text up to redirection point", 
                    st.session_state.research_content[:redirection_point],
                    height=150
                )
                
                # New instruction for redirection
                new_instruction = st.text_area("New instruction for redirection", height=100)
                
                # Select a browser snippet for context (if any)
                snippet_options = {0: "None"}
                for snippet in st.session_state.browser_snippets:
                    snippet_options[snippet["id"]] = f"{snippet['content'][:50]}..."
                
                selected_snippet_id = st.selectbox(
                    "Select browser snippet for context",
                    options=list(snippet_options.keys()),
                    format_func=lambda x: snippet_options.get(x, "")
                )
                
                if st.button("Apply Redirection"):
                    if new_instruction:
                        # Get the current progress ID (latest one)
                        current_progress_