import re
from datetime import datetime

class BrowserIntegration:
    def __init__(self):
        """Initialize browser integration"""
        pass
    
    def process_selection(self, text):
        """Process text selection from browser"""
        # Add metadata to the selection
        processed_text = {
            "content": text,
            "source": "browser_selection",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return processed_text
    
    def extract_urls(self, text):
        """Extract URLs from text"""
        url_pattern = r'https?://[\w\.-]+\.[a-zA-Z]{2,}[\w\.-/]*'
        urls = re.findall(url_pattern, text)
        return urls
    
    def extract_metadata(self, text):
        """Extract potential metadata from text"""
        # This is a placeholder for more advanced metadata extraction
        metadata = {
            "word_count": len(text.split()),
            "character_count": len(text),
            "has_urls": len(self.extract_urls(text)) > 0
        }
        return metadata