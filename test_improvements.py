#!/usr/bin/env python3
"""
Test script for GigBus browser extension improvements
Tests the new features including auto-save, multiple redirections, and text editing
"""

import requests
import json
import time
import sys

# Configuration
API_URL = "http://localhost:8000"
APP_URL = "http://localhost:8501"

def test_api_connection():
    """Test if the API is running"""
    try:
        response = requests.get(f"{API_URL}/sessions/")
        if response.status_code == 200:
            print("✅ API connection successful")
            return True
        else:
            print(f"❌ API connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API connection error: {str(e)}")
        return False

def test_session_creation():
    """Test creating a new session"""
    try:
        session_data = {
            "title": f"Test Session {int(time.time())}"
        }
        response = requests.post(f"{API_URL}/sessions/", json=session_data)
        
        if response.status_code == 200:
            session = response.json()
            print(f"✅ Session created successfully: {session['title']} (ID: {session['id']})")
            return session
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Session creation error: {str(e)}")
        return None

def test_extension_sessions_endpoint():
    """Test the extension sessions endpoint"""
    try:
        response = requests.get(f"{API_URL}/extension/sessions")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                sessions = data.get("sessions", [])
                print(f"✅ Extension sessions endpoint working: {len(sessions)} sessions found")
                return sessions
            else:
                print(f"❌ Extension sessions endpoint failed: {data.get('error')}")
                return []
        else:
            print(f"❌ Extension sessions endpoint failed: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Extension sessions endpoint error: {str(e)}")
        return []

def test_auto_save_session(session_id):
    """Test the auto-save session endpoint"""
    try:
        data = {"session_id": session_id}
        response = requests.post(f"{API_URL}/extension/auto-save-session", json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Auto-save session working: {result.get('message')}")
                return True
            else:
                print(f"❌ Auto-save session failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Auto-save session failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Auto-save session error: {str(e)}")
        return False

def test_snippet_creation(session_id):
    """Test creating a browser snippet"""
    try:
        snippet_data = {
            "session_id": session_id,
            "content": "This is a test snippet from the browser extension improvements test.",
            "source_url": "https://example.com/test"
        }
        response = requests.post(f"{API_URL}/extension/add-snippet", json=snippet_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Snippet creation working: {result.get('message')}")
                return result.get("snippet_id")
            else:
                print(f"❌ Snippet creation failed: {result.get('error')}")
                return None
        else:
            print(f"❌ Snippet creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Snippet creation error: {str(e)}")
        return None

def test_multiple_redirections(session_id):
    """Test the multiple redirections endpoint"""
    try:
        test_content = """This is a test content for multiple redirections. 
        This is the first part of the content that should remain unchanged.
        This is the second part where we want to add a redirection.
        This is the third part that should also remain unchanged.
        This is the fourth part where we want another redirection."""
        
        redirection_points = [
            {
                "position": 100,
                "instruction": "Continue with more details about the first redirection point"
            },
            {
                "position": 200,
                "instruction": "Add information about the second redirection point"
            }
        ]
        
        data = {
            "session_id": session_id,
            "content": test_content,
            "redirection_points": redirection_points
        }
        
        response = requests.post(f"{API_URL}/extension/multiple-redirections", json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Multiple redirections endpoint working")
                print(f"   Original content length: {len(test_content)}")
                processed_content = result.get("processed_content", "")
                print(f"   Processed content length: {len(processed_content)}")
                return True
            else:
                print(f"❌ Multiple redirections failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Multiple redirections failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Multiple redirections error: {str(e)}")
        return False

def test_research_generation(session_id):
    """Test the research generation endpoint"""
    try:
        research_data = {
            "prompt": "Tell me about artificial intelligence and machine learning"
        }
        response = requests.post(f"{API_URL}/research/generate", json=research_data)
        
        if response.status_code == 200:
            result = response.json()
            content = result.get("content", "")
            if content:
                print(f"✅ Research generation working: Generated {len(content)} characters")
                
                # Save as progress
                progress_data = {
                    "session_id": session_id,
                    "content": content
                }
                progress_response = requests.post(f"{API_URL}/progress/", json=progress_data)
                if progress_response.status_code == 200:
                    print("✅ Progress saving working")
                    return content
                else:
                    print(f"❌ Progress saving failed: {progress_response.status_code}")
                    return content
            else:
                print("❌ Research generation returned empty content")
                return None
        else:
            print(f"❌ Research generation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Research generation error: {str(e)}")
        return None

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting GigBus Extension Improvements Tests\n")
    
    # Test 1: API Connection
    print("1. Testing API connection...")
    if not test_api_connection():
        print("❌ Cannot proceed without API connection")
        return False
    
    # Test 2: Session Creation
    print("\n2. Testing session creation...")
    session = test_session_creation()
    if not session:
        print("❌ Cannot proceed without session")
        return False
    
    session_id = session["id"]
    
    # Test 3: Extension Sessions Endpoint
    print("\n3. Testing extension sessions endpoint...")
    sessions = test_extension_sessions_endpoint()
    
    # Test 4: Auto-save Session
    print("\n4. Testing auto-save session...")
    test_auto_save_session(session_id)
    
    # Test 5: Snippet Creation
    print("\n5. Testing snippet creation...")
    snippet_id = test_snippet_creation(session_id)
    
    # Test 6: Multiple Redirections
    print("\n6. Testing multiple redirections...")
    test_multiple_redirections(session_id)
    
    # Test 7: Research Generation
    print("\n7. Testing research generation...")
    research_content = test_research_generation(session_id)
    
    print("\n🎉 All tests completed!")
    print(f"   Session ID: {session_id}")
    if snippet_id:
        print(f"   Snippet ID: {snippet_id}")
    if research_content:
        print(f"   Research content generated: {len(research_content)} characters")
    
    print(f"\n📱 You can now test the browser extension with session ID: {session_id}")
    print(f"🌐 Open the Streamlit app at: {APP_URL}")
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
