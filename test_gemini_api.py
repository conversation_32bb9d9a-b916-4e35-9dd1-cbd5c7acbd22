import os
from dotenv import load_dotenv
from google import genai
import sys

def test_gemini_api():
    """Test if the Gemini API key is working properly"""
    print("Testing Gemini API connection...")
    
    # Load environment variables
    load_dotenv()
    
    # Get API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("ERROR: GEMINI_API_KEY not found in environment variables")
        print("Please make sure you have a .env file with GEMINI_API_KEY=your_api_key")
        return False
    
    print(f"API key found: {api_key[:5]}...{api_key[-5:]}")
    
    try:
        # Initialize the client
        client = genai.Client(api_key=api_key)
        
        # Test a simple generation
        print("Testing simple content generation...")
        response = client.models.generate_content(
            model="gemini-2.5-flash-preview-04-17",
            contents=["Hello, please respond with a short greeting."]
        )
        
        print(f"Response received: {response.text}")
        print("API test successful!")
        return True
    except Exception as e:
        print(f"ERROR: Failed to connect to Gemini API: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_gemini_api()
    sys.exit(0 if success else 1)
