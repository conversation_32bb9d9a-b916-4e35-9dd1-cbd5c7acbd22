import os
from google import genai
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("gemini_client")

class GeminiClient:
    """Client for interacting with Google's Gemini API"""

    def __init__(self):
        """Initialize the Gemini client with API key from environment variables"""
        # Load environment variables
        load_dotenv()

        # Get API key
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("GEMINI_API_KEY not found in environment variables")
            raise ValueError("GEMINI_API_KEY not found in environment variables")

        # Initialize the client
        self.client = genai.Client(api_key=api_key)
        self.model_name = "gemini-2.5-flash-preview-04-17"  # Default model
        logger.info(f"Gemini client initialized with model: {self.model_name}")

    def _prepare_content(self, prompt, context=None):
        """Prepare content with optional context"""
        if not context:
            return [prompt]

        if isinstance(context, list) and len(context) > 0:
            context_text = "\n\nContext:\n" + "\n\n".join(
                [c["content"] if isinstance(c, dict) else c for c in context]
            )
            return [f"{prompt}{context_text}"]

        return [prompt]

    def generate_content(self, prompt, context=None):
        """Generate content using Gemini API with optional context"""
        try:
            logger.info(f"Generating content for prompt: {prompt[:50]}...")

            # Prepare content
            contents = self._prepare_content(prompt, context)

            # Generate response
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=contents
            )

            logger.info("Content generation successful")
            return response.text
        except Exception as e:
            logger.error(f"Error generating content: {str(e)}")
            return f"Error generating content: {str(e)}"

    def generate_content_stream(self, prompt, context=None):
        """Generate streaming content using Gemini API with optional context"""
        try:
            logger.info(f"Streaming content for prompt: {prompt[:50]}...")

            # Prepare content
            contents = self._prepare_content(prompt, context)

            # Generate streaming response
            response = self.client.models.generate_content_stream(
                model=self.model_name,
                contents=contents
            )

            # Process the stream
            chunk_count = 0
            for chunk in response:
                if chunk.text:
                    chunk_count += 1
                    if chunk_count % 10 == 0:
                        logger.debug(f"Received chunk {chunk_count}: {chunk.text[:30]}...")
                    yield chunk.text

            logger.info(f"Streaming complete, sent {chunk_count} chunks")
        except Exception as e:
            logger.error(f"Error streaming content: {str(e)}")
            yield f"Error streaming content: {str(e)}"

    def chat_generate_content(self, message, history=None):
        """Generate content in chat mode with optional history"""
        try:
            logger.info(f"Generating chat response for message: {message[:50]}...")

            # Initialize or use existing chat session
            if not hasattr(self, 'chat_session') or self.chat_session is None:
                self.chat_session = self.client.models.start_chat(
                    model=self.model_name,
                    history=history
                )
                logger.info("New chat session initialized")

            # Send message to chat session
            response = self.chat_session.send_message(message)

            logger.info("Chat response generated successfully")
            return response.text
        except Exception as e:
            logger.error(f"Error in chat: {str(e)}")
            return f"Error in chat: {str(e)}"

# For testing
if __name__ == "__main__":
    client = GeminiClient()
    response = client.generate_content("Explain what color grading is in photography")
    print(response)