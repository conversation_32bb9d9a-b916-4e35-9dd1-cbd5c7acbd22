import os
from dotenv import load_dotenv
from google import genai
import sys

def test_gemini_streaming():
    """Test if the Gemini API streaming is working properly"""
    print("Testing Gemini API streaming...")

    # Load environment variables
    load_dotenv()

    # Get API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("ERROR: GEMINI_API_KEY not found in environment variables")
        print("Please make sure you have a .env file with GEMINI_API_KEY=your_api_key")
        return False

    print(f"API key found: {api_key[:5]}...{api_key[-5:]}")

    try:
        # Initialize the client
        client = genai.Client(api_key=api_key)

        # Test streaming generation
        print("Testing streaming content generation...")

        prompt = "Explain the basics of color grading in photography in 5 steps"
        print(f"Prompt: {prompt}")

        response = client.models.generate_content_stream(
            model="gemini-2.5-flash-preview-04-17",
            contents=[prompt]
        )

        print("Streaming response:")
        chunk_count = 0
        for chunk in response:
            if chunk.text:
                chunk_count += 1
                print(f"Chunk {chunk_count}: {chunk.text}")

        print(f"Received {chunk_count} chunks")
        print("Streaming test successful!")
        return True
    except Exception as e:
        print(f"ERROR: Failed to stream from Gemini API: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_gemini_streaming()
    sys.exit(0 if success else 1)
