// Background script for GigBus Snippet Collector

// API endpoint
const API_URL = 'http://localhost:8000';

// Create context menu items when extension is installed
chrome.runtime.onInstalled.addListener(() => {
  // Main menu item
  chrome.contextMenus.create({
    id: 'addToGigBus',
    title: 'Add to GigBus',
    contexts: ['selection']
  });

  // Analysis submenu
  chrome.contextMenus.create({
    id: 'analyzeText',
    title: 'Analyze Text',
    contexts: ['selection']
  });

  // Analysis options
  chrome.contextMenus.create({
    id: 'analyzeSentiment',
    parentId: 'analyzeText',
    title: 'Sentiment Analysis',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'analyzeWordCount',
    parentId: 'analyzeText',
    title: 'Word Count Analysis',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'analyzeReadability',
    parentId: 'analyzeText',
    title: 'Readability Analysis',
    contexts: ['selection']
  });

  chrome.contextMenus.create({
    id: 'analyzeKeywords',
    parentId: 'analyzeText',
    title: 'Keyword Extraction',
    contexts: ['selection']
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'addToGigBus') {
    handleAddToGigBus(info, tab);
  } else if (info.menuItemId.startsWith('analyze')) {
    handleAnalysis(info, tab);
  }
});

// Handle adding snippet to GigBus
function handleAddToGigBus(info, tab) {
  // Get the selected text
  const selectedText = info.selectionText;

  // Get the current URL
  const sourceUrl = tab.url;

  // Get the current session ID from storage
  chrome.storage.local.get(['currentSessionId'], (result) => {
    const sessionId = result.currentSessionId;

    if (!sessionId) {
      // If no session is selected, show a notification
      chrome.action.setBadgeText({ text: '!' });
      chrome.action.setBadgeBackgroundColor({ color: '#FF0000' });
      return;
    }

    // Send the snippet to the API
    fetch(`${API_URL}/extension/add-snippet`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        session_id: sessionId,
        content: selectedText,
        source_url: sourceUrl
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success badge
        chrome.action.setBadgeText({ text: '✓' });
        chrome.action.setBadgeBackgroundColor({ color: '#00FF00' });

        // Clear badge after 2 seconds
        setTimeout(() => {
          chrome.action.setBadgeText({ text: '' });
        }, 2000);
      } else {
        // Show error badge
        chrome.action.setBadgeText({ text: '✗' });
        chrome.action.setBadgeBackgroundColor({ color: '#FF0000' });
      }
    })
    .catch(error => {
      console.error('Error adding snippet:', error);
      // Show error badge
      chrome.action.setBadgeText({ text: '✗' });
      chrome.action.setBadgeBackgroundColor({ color: '#FF0000' });
    });
  });
}

// Handle text analysis
function handleAnalysis(info, tab) {
  const selectedText = info.selectionText;
  let analysisType = '';

  // Determine analysis type from menu item ID
  switch (info.menuItemId) {
    case 'analyzeSentiment':
      analysisType = 'sentiment';
      break;
    case 'analyzeWordCount':
      analysisType = 'wordcount';
      break;
    case 'analyzeReadability':
      analysisType = 'readability';
      break;
    case 'analyzeKeywords':
      analysisType = 'keywords';
      break;
    default:
      return;
  }

  // Send analysis request to content script
  chrome.tabs.sendMessage(tab.id, {
    action: 'performAnalysis',
    analysisType: analysisType,
    text: selectedText
  });
}

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'executeAnalysis') {
    executeAnalysis(request, sendResponse);
    return true; // Keep message channel open for async response
  } else if (request.action === 'sendAnalysisToGigBus') {
    sendAnalysisToGigBus(request, sendResponse);
    return true; // Keep message channel open for async response
  }
});

// Execute analysis code
function executeAnalysis(request, sendResponse) {
  const { analysisType, text, code, operationName } = request;

  try {
    // Create a safe execution environment using eval (for demonstration)
    // In production, you might want to use a more secure sandbox
    const result = executeCodeSafely(code, text);

    // Send result back to content script
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'showAnalysisResult',
          title: operationName,
          result: result
        });
      }
    });

    sendResponse({ success: true });
  } catch (error) {
    console.error('Error executing analysis:', error);

    // Send error back to content script
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'showAnalysisResult',
          title: 'Analysis Error',
          result: `Error: ${error.message}`
        });
      }
    });

    sendResponse({ success: false, error: error.message });
  }
}

// Execute code safely (simplified version)
function executeCodeSafely(code, text) {
  // Convert Python-like code to JavaScript
  let jsCode = code
    .replace(/# /g, '// ')
    .replace(/def /g, 'function ')
    .replace(/import re/g, '')
    .replace(/f"/g, '`')
    .replace(/f'/g, '`')
    .replace(/{([^}]+)}/g, '${$1}')
    .replace(/\\n/g, '\\n')
    .replace(/len\(/g, 'length(')
    .replace(/\.lower\(\)/g, '.toLowerCase()')
    .replace(/\.split\(\)/g, '.split(" ")')
    .replace(/\.strip\(\)/g, '.trim()')
    .replace(/\.replace\(/g, '.replace(')
    .replace(/sum\(/g, 'sum(')
    .replace(/max\(/g, 'Math.max(')
    .replace(/min\(/g, 'Math.min(')
    .replace(/round\(/g, 'Math.round(');

  // Create execution context
  const context = {
    text: text,
    result: '',
    length: (arr) => Array.isArray(arr) ? arr.length : arr.toString().length,
    sum: (arr) => arr.reduce((a, b) => a + b, 0),
    range: (start, end) => Array.from({ length: end - start }, (_, i) => start + i)
  };

  // Simple execution (this is a basic implementation)
  // For production, use a proper sandboxed environment
  try {
    // Execute simplified analysis
    if (code.includes('sentiment')) {
      return executeSentimentAnalysis(text);
    } else if (code.includes('words')) {
      return executeWordCountAnalysis(text);
    } else if (code.includes('readability')) {
      return executeReadabilityAnalysis(text);
    } else if (code.includes('keywords')) {
      return executeKeywordAnalysis(text);
    }

    return 'Analysis completed';
  } catch (error) {
    return `Error: ${error.message}`;
  }
}

// Simplified analysis implementations
function executeSentimentAnalysis(text) {
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'joy', 'positive', 'best', 'awesome', 'brilliant', 'perfect', 'outstanding'];
  const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'worst', 'horrible', 'disgusting', 'negative', 'poor', 'disappointing', 'frustrating'];

  const textLower = text.toLowerCase();
  const positiveCount = positiveWords.filter(word => textLower.includes(word)).length;
  const negativeCount = negativeWords.filter(word => textLower.includes(word)).length;

  let sentiment, score;
  if (positiveCount > negativeCount) {
    sentiment = "Positive";
    score = positiveCount - negativeCount;
  } else if (negativeCount > positiveCount) {
    sentiment = "Negative";
    score = negativeCount - positiveCount;
  } else {
    sentiment = "Neutral";
    score = 0;
  }

  return `Sentiment: ${sentiment} (Score: ${score})\nPositive words found: ${positiveCount}\nNegative words found: ${negativeCount}`;
}

function executeWordCountAnalysis(text) {
  const words = text.split(/\s+/).filter(word => word.length > 0).length;
  const characters = text.length;
  const charactersNoSpaces = text.replace(/\s/g, '').length;
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;

  return `Words: ${words}\nCharacters: ${characters}\nCharacters (no spaces): ${charactersNoSpaces}\nSentences: ${sentences}\nParagraphs: ${paragraphs}`;
}

function executeReadabilityAnalysis(text) {
  const words = text.split(/\s+/).filter(word => word.length > 0);
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

  // Simple syllable counting
  let syllables = 0;
  words.forEach(word => {
    const cleanWord = word.toLowerCase().replace(/[^a-z]/g, '');
    const vowels = 'aeiouy';
    let syllableCount = 0;
    let prevWasVowel = false;

    for (let char of cleanWord) {
      if (vowels.includes(char)) {
        if (!prevWasVowel) syllableCount++;
        prevWasVowel = true;
      } else {
        prevWasVowel = false;
      }
    }

    syllables += Math.max(syllableCount, 1);
  });

  const avgWordsPerSentence = words.length / Math.max(sentences.length, 1);
  const avgSyllablesPerWord = syllables / Math.max(words.length, 1);

  const readabilityScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);

  let level;
  if (readabilityScore >= 90) level = "Very Easy";
  else if (readabilityScore >= 80) level = "Easy";
  else if (readabilityScore >= 70) level = "Fairly Easy";
  else if (readabilityScore >= 60) level = "Standard";
  else if (readabilityScore >= 50) level = "Fairly Difficult";
  else if (readabilityScore >= 30) level = "Difficult";
  else level = "Very Difficult";

  return `Readability Score: ${Math.round(readabilityScore * 10) / 10} (${level})\nAvg words per sentence: ${Math.round(avgWordsPerSentence * 10) / 10}\nAvg syllables per word: ${Math.round(avgSyllablesPerWord * 10) / 10}`;
}

function executeKeywordAnalysis(text) {
  const words = text.toLowerCase().match(/\b[a-z]{3,}\b/g) || [];
  const stopWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those']);

  const filteredWords = words.filter(word => !stopWords.has(word));
  const wordFreq = {};

  filteredWords.forEach(word => {
    wordFreq[word] = (wordFreq[word] || 0) + 1;
  });

  const topWords = Object.entries(wordFreq)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10);

  return "Top Keywords:\n" + topWords.map(([word, count]) => `${word}: ${count}`).join('\n');
}

// Send analysis result to GigBus
function sendAnalysisToGigBus(request, sendResponse) {
  const { title, result } = request;

  // Get current session ID
  chrome.storage.local.get(['currentSessionId'], (storage) => {
    const sessionId = storage.currentSessionId;

    if (!sessionId) {
      sendResponse({ success: false, error: 'No session selected' });
      return;
    }

    // Create analysis content for GigBus
    const analysisContent = `## ${title}\n\n${result}\n\n---\n*Analysis performed by GigBus Extension*`;

    // Send to GigBus API
    fetch(`${API_URL}/extension/add-analysis`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        session_id: sessionId,
        title: title,
        content: analysisContent,
        analysis_type: title.toLowerCase().replace(/\s+/g, '_')
      })
    })
    .then(response => response.json())
    .then(data => {
      sendResponse({ success: data.success });
    })
    .catch(error => {
      console.error('Error sending analysis to GigBus:', error);
      sendResponse({ success: false, error: error.message });
    });
  });
}
