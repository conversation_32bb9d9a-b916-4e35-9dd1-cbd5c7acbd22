// Background script for GigBus Snippet Collector

// API endpoint
const API_URL = 'http://localhost:8000';

// Create context menu item when extension is installed
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'addToGigBus',
    title: 'Add to GigBus',
    contexts: ['selection']
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'addToGigBus') {
    // Get the selected text
    const selectedText = info.selectionText;
    
    // Get the current URL
    const sourceUrl = tab.url;
    
    // Get the current session ID from storage
    chrome.storage.local.get(['currentSessionId'], (result) => {
      const sessionId = result.currentSessionId;
      
      if (!sessionId) {
        // If no session is selected, show a notification
        chrome.action.setBadgeText({ text: '!' });
        chrome.action.setBadgeBackgroundColor({ color: '#FF0000' });
        return;
      }
      
      // Send the snippet to the API
      fetch(`${API_URL}/extension/add-snippet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          content: selectedText,
          source_url: sourceUrl
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success badge
          chrome.action.setBadgeText({ text: '✓' });
          chrome.action.setBadgeBackgroundColor({ color: '#00FF00' });
          
          // Clear badge after 2 seconds
          setTimeout(() => {
            chrome.action.setBadgeText({ text: '' });
          }, 2000);
        } else {
          // Show error badge
          chrome.action.setBadgeText({ text: '✗' });
          chrome.action.setBadgeBackgroundColor({ color: '#FF0000' });
        }
      })
      .catch(error => {
        console.error('Error adding snippet:', error);
        // Show error badge
        chrome.action.setBadgeText({ text: '✗' });
        chrome.action.setBadgeBackgroundColor({ color: '#FF0000' });
      });
    });
  }
});
