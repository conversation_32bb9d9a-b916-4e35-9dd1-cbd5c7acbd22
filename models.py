from sqlalchemy import create_engine, <PERSON>umn, Integer, String, Text, DateTime, <PERSON><PERSON>an, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import os

# Create SQLite database in the project directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATABASE_URL = f"sqlite:///{os.path.join(BASE_DIR, 'research.db')}"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database models
class ResearchSession(Base):
    __tablename__ = "research_sessions"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True, unique=True)  # Added unique constraint
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    progress_items = relationship("ResearchProgress", back_populates="session", cascade="all, delete-orphan")
    snippets = relationship("BrowserSnippet", back_populates="session", cascade="all, delete-orphan")

class ResearchProgress(Base):
    __tablename__ = "research_progress"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("research_sessions.id", ondelete="CASCADE"))
    content = Column(Text)
    timestamp = Column(DateTime, default=datetime.now)
    is_redirected = Column(Boolean, default=False)
    redirection_point = Column(Integer, nullable=True)

    # Relationships
    session = relationship("ResearchSession", back_populates="progress_items")

class BrowserSnippet(Base):
    __tablename__ = "browser_snippets"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("research_sessions.id", ondelete="CASCADE"))
    content = Column(Text)
    source_url = Column(String, nullable=True)
    timestamp = Column(DateTime, default=datetime.now)

    # Relationships
    session = relationship("ResearchSession", back_populates="snippets")

# Create all tables in the database
Base.metadata.create_all(bind=engine)

# Dependency to get the database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()