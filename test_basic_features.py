#!/usr/bin/env python3
"""
Basic test script for GigBus improvements
Tests the core functionality without relying on external servers
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported"""
    try:
        import streamlit as st
        print("✅ Streamlit import successful")
        
        import requests
        print("✅ Requests import successful")
        
        from datetime import datetime
        print("✅ Datetime import successful")
        
        # Test app.py imports
        sys.path.append('.')
        import app
        print("✅ App module import successful")
        
        # Test API imports
        import api
        print("✅ API module import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        return False

def test_helper_functions():
    """Test the new helper functions"""
    try:
        from app import highlight_text_with_redirection_points, apply_multiple_redirections
        
        # Test highlight function
        content = "This is a test content for highlighting redirection points."
        redirection_points = [
            {"position": 10, "instruction": "Test instruction 1"},
            {"position": 30, "instruction": "Test instruction 2"}
        ]
        
        highlighted = highlight_text_with_redirection_points(content, redirection_points)
        if "REDIRECTION POINT" in highlighted:
            print("✅ Highlight function working")
        else:
            print("❌ Highlight function not working properly")
            
        # Test apply multiple redirections function (without actual API calls)
        # This will test the function structure
        try:
            result = apply_multiple_redirections(content, [], 1)  # Empty redirections
            if result == content:
                print("✅ Apply multiple redirections function structure working")
            else:
                print("❌ Apply multiple redirections function not working properly")
        except Exception as e:
            print(f"⚠️ Apply multiple redirections function error (expected without API): {str(e)}")
        
        return True
    except Exception as e:
        print(f"❌ Helper functions error: {str(e)}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    required_files = [
        'app.py',
        'api.py',
        'extension/manifest.json',
        'extension/background.js',
        'extension/popup.html',
        'extension/popup.js',
        'extension/content.js',  # New file
        'extension/README.md',
        'test_improvements.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_extension_manifest():
    """Test that the extension manifest is properly updated"""
    try:
        import json
        with open('extension/manifest.json', 'r') as f:
            manifest = json.load(f)
        
        # Check version
        if manifest.get('version') == '1.1':
            print("✅ Extension version updated to 1.1")
        else:
            print(f"❌ Extension version not updated: {manifest.get('version')}")
        
        # Check permissions
        permissions = manifest.get('permissions', [])
        if 'scripting' in permissions:
            print("✅ Scripting permission added")
        else:
            print("❌ Scripting permission missing")
        
        # Check content scripts
        content_scripts = manifest.get('content_scripts', [])
        if content_scripts and 'content.js' in str(content_scripts):
            print("✅ Content script configured")
        else:
            print("❌ Content script not configured")
        
        return True
    except Exception as e:
        print(f"❌ Manifest test error: {str(e)}")
        return False

def test_session_state_variables():
    """Test that new session state variables are defined"""
    try:
        # Read app.py and check for new session state variables
        with open('app.py', 'r') as f:
            content = f.read()
        
        required_vars = [
            'redirection_points',
            'edit_mode',
            'edited_content'
        ]
        
        all_found = True
        for var in required_vars:
            if f'"{var}"' in content:
                print(f"✅ Session state variable '{var}' defined")
            else:
                print(f"❌ Session state variable '{var}' missing")
                all_found = False
        
        return all_found
    except Exception as e:
        print(f"❌ Session state test error: {str(e)}")
        return False

def run_basic_tests():
    """Run all basic tests"""
    print("🚀 Starting GigBus Basic Feature Tests\n")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Extension Manifest", test_extension_manifest),
        ("Session State Variables", test_session_state_variables),
        ("Helper Functions", test_helper_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {str(e)}")
    
    print(f"\n🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! The improvements are ready for testing.")
        print("\n📝 Next steps:")
        print("1. Start the API server: python api.py")
        print("2. Start the Streamlit app: streamlit run app.py")
        print("3. Load the browser extension in Chrome")
        print("4. Test the new features in the browser and app")
        return True
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
