<!DOCTYPE html>
<html>
<head>
  <title>Create Extension Icons</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; }
    canvas { border: 1px solid #ccc; margin: 10px; }
    .icon-section { margin: 20px 0; }
  </style>
</head>
<body>
  <h1>Create Extension Icons</h1>
  <p>This page creates simple icons for the GigBus extension.</p>
  
  <div class="icon-section">
    <h2>16x16 Icon</h2>
    <canvas id="canvas16" width="16" height="16"></canvas>
    <br>
    <a id="download16" href="#" download="icon16.png">Download 16x16 PNG</a>
  </div>
  
  <div class="icon-section">
    <h2>48x48 Icon</h2>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <br>
    <a id="download48" href="#" download="icon48.png">Download 48x48 PNG</a>
  </div>
  
  <div class="icon-section">
    <h2>128x128 Icon</h2>
    <canvas id="canvas128" width="128" height="128"></canvas>
    <br>
    <a id="download128" href="#" download="icon128.png">Download 128x128 PNG</a>
  </div>
  
  <script>
    function createIcon(canvas, size) {
      const ctx = canvas.getContext('2d');
      
      // Fill background with blue
      ctx.fillStyle = '#4285f4';
      ctx.fillRect(0, 0, size, size);
      
      // Add white text/symbol
      ctx.fillStyle = '#ffffff';
      ctx.font = `${Math.floor(size * 0.6)}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('G', size/2, size/2);
      
      return canvas.toDataURL('image/png');
    }
    
    // Create 16x16 icon
    const canvas16 = document.getElementById('canvas16');
    const dataUrl16 = createIcon(canvas16, 16);
    document.getElementById('download16').href = dataUrl16;
    
    // Create 48x48 icon
    const canvas48 = document.getElementById('canvas48');
    const dataUrl48 = createIcon(canvas48, 48);
    document.getElementById('download48').href = dataUrl48;
    
    // Create 128x128 icon
    const canvas128 = document.getElementById('canvas128');
    const dataUrl128 = createIcon(canvas128, 128);
    document.getElementById('download128').href = dataUrl128;
    
    console.log('Icons created successfully!');
  </script>
</body>
</html>
