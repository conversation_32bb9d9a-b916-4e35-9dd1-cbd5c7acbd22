<!DOCTYPE html>
<html>
<head>
  <title>GigBus Snippet Collector</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    h1 {
      font-size: 16px;
      margin-bottom: 15px;
    }
    select {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
    }
    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 5px;
    }
    button:hover {
      background-color: #3367d6;
    }
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .info {
      background-color: #d1ecf1;
      color: #0c5460;
    }
    .hidden {
      display: none;
    }
    .refresh-button {
      background-color: #6c757d;
      font-size: 12px;
      padding: 4px 8px;
    }
    .session-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <h1>GigBus Snippet Collector</h1>
  
  <div class="session-header">
    <label for="sessionSelect"><strong>Select Research Session:</strong></label>
    <button id="refreshButton" class="refresh-button">↻</button>
  </div>
  
  <select id="sessionSelect">
    <option value="">Loading sessions...</option>
  </select>
  
  <button id="saveButton">Save Selection</button>
  <button id="openAppButton">Open GigBus</button>
  
  <div id="statusMessage" class="status hidden"></div>
  
  <script src="popup.js"></script>
</body>
</html>
