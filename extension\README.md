# GigBus Snippet Collector

A Chrome extension that allows you to collect text snippets from web pages and send them directly to your GigBus research assistant.

## Features

- Right-click on selected text to add it to your GigBus session
- Select which research session to use
- Visual feedback when snippets are added
- Open GigBus directly from the extension

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" by toggling the switch in the top right corner
3. Click "Load unpacked" and select the `extension` folder
4. The extension should now be installed and visible in your toolbar

## Usage

1. Make sure your GigBus application is running at http://localhost:8501
2. Click the extension icon in your toolbar
3. Select a research session from the dropdown
4. Click "Save Selection" to set this as your active session
5. On any webpage, select text you want to save
6. Right-click and select "Add to GigBus"
7. The snippet will be added to your selected session

## Icon Generation

The extension includes an SVG icon and an HTML file to convert it to PNG:

1. Open `images/convert_icon.html` in a browser
2. Click the download links to save the PNG files
3. Place the PNG files in the `images` directory

## Troubleshooting

- If you see a red "!" badge on the extension icon, it means no session is selected
- If you see a red "✗" badge, there was an error adding the snippet
- Make sure the GigBus application is running at http://localhost:8501
- Check the browser console for any error messages
