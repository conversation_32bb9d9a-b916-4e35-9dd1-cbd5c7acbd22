# GigBus Snippet Collector v1.1

A Chrome extension that allows you to collect text snippets from web pages and send them directly to your GigBus research assistant with enhanced features for redirection and text editing.

## New Features in v1.1

### Auto-Save Session Selection
- Sessions are automatically saved when selected in the popup
- No need to manually click "Save Selection" button
- Instant feedback when session is auto-saved

### Enhanced Text Selection for Redirections
- Select text directly on web pages for redirection points
- Alternative to using the slider in the main app
- Visual markers show selected redirection points
- Support for multiple redirection points in a single document

### Multiple Redirection Support
- Add multiple redirection points to a single piece of content
- Choose to redirect only selected parts while keeping other parts unchanged
- Option to redirect the entire content (original functionality preserved)
- Visual indicators show all redirection points in the content

### Built-in Text Editor
- Manual text editing with basic formatting options
- Add bold text, headings, bullet points, and line breaks
- Edit mode toggle to switch between viewing and editing
- Save edited content back to the research session

## Core Features

- Right-click on selected text to add it to your GigBus session
- Select which research session to use
- Visual feedback when snippets are added
- Open GigBus directly from the extension
- Content script for advanced text selection
- Multiple redirection points management

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" by toggling the switch in the top right corner
3. Click "Load unpacked" and select the `extension` folder
4. The extension should now be installed and visible in your toolbar

## Usage

### Basic Snippet Collection
1. Make sure your GigBus application is running at http://localhost:8501
2. Click the extension icon in your toolbar
3. Select a research session from the dropdown (auto-saves automatically)
4. On any webpage, select text you want to save
5. Right-click and select "Add to GigBus"
6. The snippet will be added to your selected session

### Advanced Redirection Features
1. In the GigBus app, enable "Text Editing Mode" for manual editing
2. Use the text editor to modify content with formatting tools
3. Add multiple redirection points using the slider or text selection
4. Apply single redirections or multiple redirections at once
5. View content with visual markers showing redirection points

### Text Selection for Redirections
1. Copy text from any webpage where you want to add a redirection
2. Paste it in the "Paste selected text here" field in GigBus
3. Click "Find Text Position" to automatically set the redirection point
4. Add your redirection instruction and click "Add Redirection Point"

## Icon Generation

The extension includes an SVG icon and an HTML file to convert it to PNG:

1. Open `images/convert_icon.html` in a browser
2. Click the download links to save the PNG files
3. Place the PNG files in the `images` directory

## Requirements

- GigBus API server running on `http://localhost:8000`
- GigBus Streamlit app running on `http://localhost:8501`
- At least one research session created in GigBus
- Chrome browser with extensions enabled

## Files

- `manifest.json` - Extension configuration (updated to v1.1)
- `background.js` - Background script for context menu
- `popup.html` - Extension popup interface
- `popup.js` - Popup functionality with auto-save
- `content.js` - Content script for text selection (NEW)
- `images/` - Extension icons

## Testing

Run the test script to verify all functionality:

```bash
python test_improvements.py
```

This will test:
- API connectivity
- Session creation and management
- Auto-save functionality
- Snippet creation
- Multiple redirections
- Research generation

## API Endpoints

The extension uses these API endpoints:

- `GET /extension/sessions` - Get available sessions
- `POST /extension/add-snippet` - Add snippet from browser
- `POST /extension/auto-save-session` - Auto-save session selection
- `POST /extension/multiple-redirections` - Apply multiple redirections

## Troubleshooting

1. **Extension not working**: Check that both API and Streamlit servers are running
2. **No sessions available**: Create a session in the GigBus app first
3. **Auto-save not working**: Check browser console for errors
4. **Redirection points not showing**: Ensure content script is loaded properly
5. **Red "!" badge**: No session is selected
6. **Red "✗" badge**: Error adding the snippet
7. **Green "✓" badge**: Snippet added successfully

## Version History

- **v1.1**: Added auto-save, multiple redirections, text editing, enhanced text selection
- **v1.0**: Basic snippet collection and session management
