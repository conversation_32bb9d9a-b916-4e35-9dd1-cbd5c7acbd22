# GigBus Snippet Collector v1.2

A Chrome extension that allows you to collect text snippets from web pages and send them directly to your GigBus research assistant with enhanced features for redirection and text editing.

## New Features in v1.2

### Advanced Text Analysis
- **Right-click Analysis**: Select text and right-click for instant analysis
- **Multiple Analysis Types**: Sentiment Analysis, Word Count, Readability, Keyword Extraction
- **Local Processing**: All analysis runs locally in the browser for privacy
- **Visual Results**: Analysis results displayed with rich formatting and options

### LLM Integration for Text Editing
- **Send to LLM**: Send edited text directly to LLM as a prompt for redirection
- **Analysis-Based Redirection**: Use analysis results as redirection instructions
- **Code Generation**: Generate Python code based on analysis results
- **Code Execution**: Execute generated analysis code on your research content

### Advanced Content Management
- **Analysis Results Tab**: Dedicated tab for managing all analysis results
- **Bulk Operations**: Combine all analyses or clear them at once
- **Synthesis**: AI-powered synthesis of multiple analysis results
- **Concatenation**: Seamlessly merge analysis insights with existing research

## Previous Features (v1.1)

### Auto-Save Session Selection
- Sessions are automatically saved when selected in the popup
- No need to manually click "Save Selection" button
- Instant feedback when session is auto-saved

### Enhanced Text Selection for Redirections
- Select text directly on web pages for redirection points
- Alternative to using the slider in the main app
- Visual markers show selected redirection points
- Support for multiple redirection points in a single document

### Multiple Redirection Support
- Add multiple redirection points to a single piece of content
- Choose to redirect only selected parts while keeping other parts unchanged
- Option to redirect the entire content (original functionality preserved)
- Visual indicators show all redirection points in the content

### Built-in Text Editor
- Manual text editing with basic formatting options
- Add bold text, headings, bullet points, and line breaks
- Edit mode toggle to switch between viewing and editing
- Save edited content back to the research session

## Core Features

- Right-click on selected text to add it to your GigBus session
- Select which research session to use
- Visual feedback when snippets are added
- Open GigBus directly from the extension
- Content script for advanced text selection
- Multiple redirection points management

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" by toggling the switch in the top right corner
3. Click "Load unpacked" and select the `extension` folder
4. The extension should now be installed and visible in your toolbar

## Usage

### Basic Snippet Collection
1. Make sure your GigBus application is running at http://localhost:8501
2. Click the extension icon in your toolbar
3. Select a research session from the dropdown (auto-saves automatically)
4. On any webpage, select text you want to save
5. Right-click and select "Add to GigBus"
6. The snippet will be added to your selected session

### Advanced Redirection Features
1. In the GigBus app, enable "Text Editing Mode" for manual editing
2. Use the text editor to modify content with formatting tools
3. Add multiple redirection points using the slider or text selection
4. Apply single redirections or multiple redirections at once
5. View content with visual markers showing redirection points

### Advanced Text Analysis
1. Select any text on a webpage
2. Right-click and choose "Analyze Text"
3. Select analysis type: Sentiment, Word Count, Readability, or Keywords
4. View results in a popup with options to send to GigBus or copy
5. Check the "Analysis Results" tab in GigBus for all your analyses

### LLM Integration
1. In GigBus, enable "Text Editing Mode" and edit your content
2. Click "Send to LLM" to use edited text as a prompt for redirection
3. Use analysis results as redirection instructions with "Use as Redirection"
4. Generate and execute Python code based on analysis results
5. Combine multiple analyses for comprehensive insights

### Text Selection for Redirections
1. Copy text from any webpage where you want to add a redirection
2. Paste it in the "Paste selected text here" field in GigBus
3. Click "Find Text Position" to automatically set the redirection point
4. Add your redirection instruction and click "Add Redirection Point"

## Icon Generation

The extension includes an SVG icon and an HTML file to convert it to PNG:

1. Open `images/convert_icon.html` in a browser
2. Click the download links to save the PNG files
3. Place the PNG files in the `images` directory

## Requirements

- GigBus API server running on `http://localhost:8000`
- GigBus Streamlit app running on `http://localhost:8501`
- At least one research session created in GigBus
- Chrome browser with extensions enabled

## Files

- `manifest.json` - Extension configuration (updated to v1.1)
- `background.js` - Background script for context menu
- `popup.html` - Extension popup interface
- `popup.js` - Popup functionality with auto-save
- `content.js` - Content script for text selection (NEW)
- `images/` - Extension icons

## Testing

Run the test script to verify all functionality:

```bash
python test_improvements.py
```

This will test:
- API connectivity
- Session creation and management
- Auto-save functionality
- Snippet creation
- Multiple redirections
- Research generation

## API Endpoints

The extension uses these API endpoints:

- `GET /extension/sessions` - Get available sessions
- `POST /extension/add-snippet` - Add snippet from browser
- `POST /extension/auto-save-session` - Auto-save session selection
- `POST /extension/multiple-redirections` - Apply multiple redirections

## Troubleshooting

1. **Extension not working**: Check that both API and Streamlit servers are running
2. **No sessions available**: Create a session in the GigBus app first
3. **Auto-save not working**: Check browser console for errors
4. **Redirection points not showing**: Ensure content script is loaded properly
5. **Red "!" badge**: No session is selected
6. **Red "✗" badge**: Error adding the snippet
7. **Green "✓" badge**: Snippet added successfully

## Version History

- **v1.2**: Added advanced text analysis, LLM integration, code generation/execution, analysis synthesis
- **v1.1**: Added auto-save, multiple redirections, text editing, enhanced text selection
- **v1.0**: Basic snippet collection and session management

## Advanced Features Demo

### Text Analysis Workflow
1. **Select & Analyze**: Select text on any webpage → Right-click → "Analyze Text" → Choose analysis type
2. **Review Results**: Analysis appears in popup with detailed results and options
3. **Send to GigBus**: Click "Send to GigBus" to save analysis to your research session
4. **Use for Research**: In GigBus "Analysis Results" tab, use analysis for redirection or code generation

### LLM Integration Workflow
1. **Edit Content**: Enable "Text Editing Mode" in GigBus and modify your research content
2. **Send to LLM**: Click "Send to LLM" to use edited content as a prompt for continuation
3. **Analysis Redirection**: Use analysis results as redirection instructions for AI-guided research
4. **Code Generation**: Generate Python code based on analysis results and execute on your content

### Synthesis Workflow
1. **Collect Analyses**: Perform multiple text analyses on different web content
2. **Combine Insights**: Use "Combine All Analyses" to synthesize all results
3. **AI Synthesis**: LLM creates comprehensive insights from all your analyses
4. **Integrated Research**: Results are concatenated with your existing research content
