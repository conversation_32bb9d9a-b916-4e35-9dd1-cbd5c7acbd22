import re
import json
from datetime import datetime

def sanitize_filename(filename):
    """Sanitize a string to be used as a filename"""
    # Remove invalid characters
    filename = re.sub(r'[\\/*?:"<>|]', "", filename)
    # Limit length
    if len(filename) > 100:
        filename = filename[:100]
    return filename

def format_timestamp(timestamp_str):
    """Format timestamp string for display"""
    try:
        dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        return dt.strftime("%b %d, %Y at %I:%M %p")
    except:
        return timestamp_str

def truncate_text(text, max_length=100):
    """Truncate text to specified length with ellipsis"""
    if len(text) <= max_length:
        return text
    return text[:max_length] + "..."

def parse_json_safely(json_str):
    """Parse JSON string safely, returning None if invalid"""
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        return None