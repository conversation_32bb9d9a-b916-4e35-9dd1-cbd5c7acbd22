#!/usr/bin/env python3
"""
Test script for the integrated GigBus features
Tests that analysis tools work together with redirection and browser snippets
"""

import sys
import os

def test_imports():
    """Test that all modules can be imported"""
    try:
        import streamlit as st
        print("✅ Streamlit import successful")
        
        import requests
        print("✅ Requests import successful")
        
        from datetime import datetime
        print("✅ Datetime import successful")
        
        # Test app.py imports
        sys.path.append('.')
        import app
        print("✅ App module import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        return False

def test_integration_features():
    """Test that integration features are properly implemented"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for integrated LLM functionality
        integration_features = [
            ('Send to LLM as Redirection', 'LLM integration with redirection'),
            ('Use for Redirection', 'Analysis redirection integration'),
            ('stream_research', 'Streaming research function usage'),
            ('browser_snippets', 'Browser snippets integration'),
            ('context_snippets', 'Context snippets usage'),
            ('redirect_placeholder', 'Redirection placeholder usage'),
            ('progress_items', 'Progress items integration')
        ]
        
        all_features_present = True
        for feature, description in integration_features:
            if feature in content:
                print(f"✅ {description} found")
            else:
                print(f"❌ {description} missing")
                all_features_present = False
        
        return all_features_present
    except Exception as e:
        print(f"❌ Integration features test error: {str(e)}")
        return False

def test_redirection_integration():
    """Test that analysis tools integrate with redirection system"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for redirection integration in each analysis tool
        redirection_integrations = [
            ('redirect_sentiment', 'Sentiment analysis redirection'),
            ('redirect_readability', 'Readability analysis redirection'),
            ('redirect_wordcount', 'Word count analysis redirection'),
            ('redirect_keywords', 'Keyword analysis redirection'),
            ('Execute & Use for Redirection', 'Code execution redirection')
        ]
        
        all_integrations_present = True
        for integration, description in redirection_integrations:
            if integration in content:
                print(f"✅ {description} integrated")
            else:
                print(f"❌ {description} not integrated")
                all_integrations_present = False
        
        return all_integrations_present
    except Exception as e:
        print(f"❌ Redirection integration test error: {str(e)}")
        return False

def test_browser_snippets_integration():
    """Test that browser snippets are integrated with analysis"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for browser snippets context usage
        snippet_integrations = [
            ('browser_snippets[-3:]', 'Recent snippets usage in LLM redirection'),
            ('browser_snippets[-2:]', 'Recent snippets usage in analysis redirection'),
            ('Context from', 'Context formatting for snippets'),
            ('Additional context:', 'Context inclusion in prompts')
        ]
        
        all_integrations_present = True
        for integration, description in snippet_integrations:
            if integration in content:
                print(f"✅ {description} implemented")
            else:
                print(f"❌ {description} not implemented")
                all_integrations_present = False
        
        return all_integrations_present
    except Exception as e:
        print(f"❌ Browser snippets integration test error: {str(e)}")
        return False

def test_unified_workflow():
    """Test that the workflow is unified and not separate"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that separate progress creation is removed
        separate_indicators = [
            'create_progress(st.session_state.current_session_id, llm_response)',
            'Text sent to LLM and response saved!',
            'separate LLM response'
        ]
        
        has_separate_workflow = any(indicator in content for indicator in separate_indicators)
        
        if not has_separate_workflow:
            print("✅ Separate workflow removed - unified with redirection")
        else:
            print("❌ Still has separate workflow elements")
        
        # Check for unified workflow elements
        unified_elements = [
            'stream_research(',
            'latest_progress = st.session_state.progress_items[-1]',
            'st.session_state.research_content = latest_progress["content"]',
            'st.session_state.edited_content = latest_progress["content"]'
        ]
        
        unified_count = sum(1 for element in unified_elements if element in content)
        
        if unified_count >= 3:
            print(f"✅ Unified workflow elements present ({unified_count}/4)")
            return True
        else:
            print(f"❌ Insufficient unified workflow elements ({unified_count}/4)")
            return False
            
    except Exception as e:
        print(f"❌ Unified workflow test error: {str(e)}")
        return False

def test_analysis_functions():
    """Test that analysis functions are still working"""
    try:
        from app import (
            perform_sentiment_analysis,
            perform_word_count_analysis,
            perform_readability_analysis,
            perform_keyword_analysis
        )
        
        test_text = "This is a wonderful test for our integrated analysis system. It should work seamlessly with the redirection mechanism."
        
        # Test each analysis function
        sentiment_result = perform_sentiment_analysis(test_text)
        if "Sentiment:" in sentiment_result:
            print("✅ Sentiment analysis function working")
        else:
            print("❌ Sentiment analysis function failed")
            
        wordcount_result = perform_word_count_analysis(test_text)
        if "Words:" in wordcount_result:
            print("✅ Word count analysis function working")
        else:
            print("❌ Word count analysis function failed")
            
        readability_result = perform_readability_analysis(test_text)
        if "Readability Score:" in readability_result:
            print("✅ Readability analysis function working")
        else:
            print("❌ Readability analysis function failed")
            
        keyword_result = perform_keyword_analysis(test_text)
        if "Top Keywords:" in keyword_result:
            print("✅ Keyword analysis function working")
        else:
            print("❌ Keyword analysis function failed")
        
        return True
    except Exception as e:
        print(f"❌ Analysis functions error: {str(e)}")
        return False

def run_integration_tests():
    """Run all integration tests"""
    print("🚀 Starting GigBus Integration Tests\n")
    
    tests = [
        ("Module Imports", test_imports),
        ("Integration Features", test_integration_features),
        ("Redirection Integration", test_redirection_integration),
        ("Browser Snippets Integration", test_browser_snippets_integration),
        ("Unified Workflow", test_unified_workflow),
        ("Analysis Functions", test_analysis_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {str(e)}")
    
    print(f"\n🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! The unified system is working correctly.")
        print("\n📝 Integrated Features:")
        print("1. ✨ 'Send to LLM as Redirection' - Uses existing redirection system")
        print("2. 🔍 Analysis tools with 'Use for Redirection' buttons")
        print("3. 🤖 Code execution with redirection integration")
        print("4. 📊 Browser snippets automatically included as context")
        print("5. 🔄 Unified progress tracking and content updates")
        print("6. 🎯 No separate workflows - everything integrated")
        
        print("\n🚀 How the integration works:")
        print("1. Edit content in text editor")
        print("2. Use 'Send to LLM as Redirection' - integrates with redirection system")
        print("3. Run analysis tools - each has 'Use for Redirection' option")
        print("4. Browser snippets automatically included as context")
        print("5. All results flow through unified progress system")
        print("6. Research content and edited content stay synchronized")
        
        return True
    else:
        print("⚠️ Some integration tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
