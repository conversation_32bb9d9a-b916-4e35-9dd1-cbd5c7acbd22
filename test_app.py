import streamlit as st
import requests
import json

# API URL
API_URL = "http://localhost:8000"

st.title("Test Gemini Streaming")

# Create a text input for the prompt
prompt = st.text_input("Enter your prompt", "Explain color grading in photography")

# Create a button to start streaming
if st.button("Start Streaming"):
    # Create a status indicator
    status = st.info("Starting streaming...")
    
    # Create a placeholder for the content
    content_placeholder = st.empty()
    content = ""
    
    # Create a debug expander
    with st.expander("Debug Information"):
        st.write(f"Sending request to {API_URL}/research/stream")
        st.write(f"Prompt: {prompt}")
    
    try:
        # Make a request to the API
        with requests.post(
            f"{API_URL}/research/stream",
            json={"prompt": prompt},
            headers={"Content-Type": "application/json"},
            stream=True
        ) as response:
            if response.status_code != 200:
                st.error(f"Error: {response.status_code} - {response.text}")
                status.error("Streaming failed!")
            else:
                # Process the server-sent events
                buffer = ""
                chunk_count = 0
                
                for line in response.iter_lines():
                    if line:
                        line_str = line.decode('utf-8')
                        with st.expander("Debug Information"):
                            st.write(f"Received line: {line_str}")
                        
                        buffer += line_str + "\n"
                        
                        if buffer.endswith("\n\n"):
                            if buffer.startswith("data: "):
                                try:
                                    data = json.loads(buffer[6:].strip())
                                    chunk = data.get("content", "")
                                    done = data.get("done", False)
                                    
                                    chunk_count += 1
                                    
                                    if chunk:
                                        content += chunk
                                        content_placeholder.markdown(content)
                                        with st.expander("Debug Information"):
                                            st.write(f"Chunk {chunk_count}: {chunk}")
                                    
                                    if done:
                                        status.success("Streaming complete!")
                                        break
                                except json.JSONDecodeError as e:
                                    with st.expander("Debug Information"):
                                        st.error(f"Error parsing JSON: {e} - Content: {buffer}")
                            buffer = ""
                
                with st.expander("Debug Information"):
                    st.write(f"Received {chunk_count} chunks")
    except Exception as e:
        st.error(f"Error: {str(e)}")
        status.error("Streaming failed!")
        with st.expander("Debug Information"):
            import traceback
            st.write(traceback.format_exc())
