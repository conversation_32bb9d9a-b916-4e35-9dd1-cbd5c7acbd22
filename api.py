from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import json
import logging
import asyncio
import models
from models import get_db
from gemini_client import Gemini<PERSON>lient
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("api")

# Initialize FastAPI app
app = FastAPI(title="Deep Research Assistant API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development - restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Gemini client
gemini_client = GeminiClient()

# Pydantic models for request/response
class ResearchSessionCreate(BaseModel):
    title: str

class ResearchSessionResponse(BaseModel):
    id: int
    title: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  # Replaces orm_mode=True in Pydantic v2

class ResearchProgressCreate(BaseModel):
    session_id: int
    content: str

class ResearchProgressResponse(BaseModel):
    id: int
    session_id: int
    content: str
    timestamp: datetime
    is_redirected: bool
    redirection_point: Optional[int] = None

    class Config:
        from_attributes = True

class BrowserSnippetCreate(BaseModel):
    session_id: int
    content: str
    source_url: Optional[str] = None

class BrowserSnippetResponse(BaseModel):
    id: int
    session_id: int
    content: str
    source_url: Optional[str]
    timestamp: datetime

    class Config:
        from_attributes = True

class RedirectionRequest(BaseModel):
    session_id: int
    progress_id: int
    redirection_point: int  # Index where to cut the text
    new_instruction: str
    browser_snippet_id: Optional[int] = None

class ResearchRequest(BaseModel):
    prompt: str
    context: Optional[List[str]] = None

# CRUD operations for ResearchSession
@app.post("/sessions/", response_model=ResearchSessionResponse)
def create_session(session: ResearchSessionCreate, db: Session = Depends(get_db)):
    """Create a new research session"""
    # Check if session with this title already exists
    existing_session = db.query(models.ResearchSession).filter(
        models.ResearchSession.title == session.title
    ).first()

    if existing_session:
        logger.info(f"Session with title '{session.title}' already exists, returning existing session")
        return existing_session

    # Create new session
    db_session = models.ResearchSession(title=session.title)
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    logger.info(f"Created new session: {session.title} (ID: {db_session.id})")
    return db_session

@app.get("/sessions/", response_model=List[ResearchSessionResponse])
def read_sessions(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """Get all research sessions"""
    sessions = db.query(models.ResearchSession).offset(skip).limit(limit).all()
    logger.info(f"Retrieved {len(sessions)} sessions")
    return sessions

@app.get("/sessions/{session_id}", response_model=ResearchSessionResponse)
def read_session(session_id: int, db: Session = Depends(get_db)):
    session = db.query(models.ResearchSession).filter(models.ResearchSession.id == session_id).first()
    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")
    return session

@app.delete("/sessions/{session_id}")
def delete_session(session_id: int, db: Session = Depends(get_db)):
    session = db.query(models.ResearchSession).filter(models.ResearchSession.id == session_id).first()
    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")
    db.delete(session)
    db.commit()
    return {"message": "Session deleted successfully"}

# CRUD operations for ResearchProgress
@app.post("/progress/", response_model=ResearchProgressResponse)
def create_progress(progress: ResearchProgressCreate, db: Session = Depends(get_db)):
    # Check if session exists
    session = db.query(models.ResearchSession).filter(models.ResearchSession.id == progress.session_id).first()
    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")

    db_progress = models.ResearchProgress(**progress.model_dump())
    db.add(db_progress)
    db.commit()
    db.refresh(db_progress)
    return db_progress

@app.get("/progress/session/{session_id}", response_model=List[ResearchProgressResponse])
def read_progress_by_session(session_id: int, db: Session = Depends(get_db)):
    progress_items = db.query(models.ResearchProgress).filter(
        models.ResearchProgress.session_id == session_id
    ).order_by(models.ResearchProgress.timestamp).all()
    return progress_items

@app.delete("/progress/{progress_id}")
def delete_progress(progress_id: int, db: Session = Depends(get_db)):
    progress = db.query(models.ResearchProgress).filter(models.ResearchProgress.id == progress_id).first()
    if progress is None:
        raise HTTPException(status_code=404, detail="Progress not found")
    db.delete(progress)
    db.commit()
    return {"message": "Progress deleted successfully"}

# CRUD operations for BrowserSnippet
@app.post("/snippets/", response_model=BrowserSnippetResponse)
def create_snippet(snippet: BrowserSnippetCreate, db: Session = Depends(get_db)):
    # Check if session exists
    session = db.query(models.ResearchSession).filter(models.ResearchSession.id == snippet.session_id).first()
    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")

    db_snippet = models.BrowserSnippet(**snippet.model_dump())
    db.add(db_snippet)
    db.commit()
    db.refresh(db_snippet)
    return db_snippet

@app.get("/snippets/session/{session_id}", response_model=List[BrowserSnippetResponse])
def read_snippets_by_session(session_id: int, db: Session = Depends(get_db)):
    snippets = db.query(models.BrowserSnippet).filter(
        models.BrowserSnippet.session_id == session_id
    ).order_by(models.BrowserSnippet.timestamp).all()
    return snippets

@app.delete("/snippets/{snippet_id}")
def delete_snippet(snippet_id: int, db: Session = Depends(get_db)):
    snippet = db.query(models.BrowserSnippet).filter(models.BrowserSnippet.id == snippet_id).first()
    if snippet is None:
        raise HTTPException(status_code=404, detail="Snippet not found")
    db.delete(snippet)
    db.commit()
    return {"message": "Snippet deleted successfully"}

# Redirection mechanism
@app.post("/redirect/", response_model=ResearchProgressResponse)
def create_redirection(redirection: RedirectionRequest, db: Session = Depends(get_db)):
    # Get the progress item to redirect
    progress = db.query(models.ResearchProgress).filter(models.ResearchProgress.id == redirection.progress_id).first()
    if progress is None:
        raise HTTPException(status_code=404, detail="Progress not found")

    # Get the browser snippet if provided
    snippet_content = ""
    if redirection.browser_snippet_id:
        snippet = db.query(models.BrowserSnippet).filter(
            models.BrowserSnippet.id == redirection.browser_snippet_id
        ).first()
        if snippet:
            snippet_content = f"\n\nBrowser Context: {snippet.content}"

    # Create a new progress item with redirected content
    original_content = progress.content
    if redirection.redirection_point >= len(original_content):
        raise HTTPException(status_code=400, detail="Redirection point exceeds content length")

    # Cut the content at the redirection point and add new instruction and snippet
    redirected_content = original_content[:redirection.redirection_point] + "\n\n" + \
                        f"Redirection: {redirection.new_instruction}" + snippet_content

    # Create a new progress item
    new_progress = models.ResearchProgress(
        session_id=redirection.session_id,
        content=redirected_content,
        is_redirected=True,
        redirection_point=redirection.redirection_point
    )

    db.add(new_progress)
    db.commit()
    db.refresh(new_progress)
    return new_progress

# Gemini API integration endpoints
@app.post("/research/generate")
async def generate_research(request: ResearchRequest):
    """Generate research content using Gemini API"""
    prompt = request.prompt
    context = request.context

    if not prompt:
        logger.warning("Research generation attempted with empty prompt")
        raise HTTPException(status_code=400, detail="Prompt is required")

    logger.info(f"Generating research for prompt: {prompt[:50]}...")
    response = gemini_client.generate_content(prompt, context)
    logger.info("Research generation complete")
    return {"content": response}

@app.post("/research/stream")
async def stream_research(request: Request):
    """Stream research content using Gemini API"""
    data = await request.json()
    prompt = data.get("prompt", "")
    context = data.get("context", None)
    redirection = data.get("redirection", None)

    logger.info(f"Received research streaming request with prompt: {prompt[:50]}...")

    # For redirection, we need either a prompt or redirection data
    if not prompt and not redirection:
        logger.warning("Research streaming attempted without prompt or redirection")
        raise HTTPException(status_code=400, detail="Either prompt or redirection data is required")

    # If redirection is provided, modify the prompt
    if redirection:
        redirection_point = redirection.get("redirection_point", 0)
        new_instruction = redirection.get("new_instruction", "")
        original_content = redirection.get("original_content", "")

        logger.info(f"Redirection data received: point={redirection_point}, instruction={new_instruction[:30]}...")

        if redirection_point > 0 and new_instruction and original_content:
            # Cut the content at the redirection point and add new instruction
            prompt = original_content[:redirection_point] + "\n\n" + f"Redirection: {new_instruction}"
            # If we have context, add it to the prompt
            if context:
                prompt += "\n\nContext: " + "\n".join(context)

            logger.info(f"Modified prompt with redirection: {prompt[:50]}...")

    # Define the streaming generator function
    async def event_generator():
        try:
            # Send an initial event to establish the connection
            logger.info("Starting stream, sending initial event...")
            yield f"data: {json.dumps({'content': 'Starting research...'})}\n\n"

            # Get the generator from Gemini client
            generator = gemini_client.generate_content_stream(prompt, context)

            # Stream the content
            chunk_count = 0
            for chunk in generator:
                chunk_count += 1
                if chunk_count % 10 == 0:
                    logger.debug(f"Sending chunk {chunk_count} to client")

                yield f"data: {json.dumps({'content': chunk})}\n\n"
                # Add a small delay to ensure the client receives each chunk
                await asyncio.sleep(0.01)

            # Send a final event to indicate completion
            logger.info(f"Streaming complete, sent {chunk_count} chunks")
            yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"
        except Exception as e:
            logger.error(f"Error in streaming: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Send error message to client
            yield f"data: {json.dumps({'content': f'Error: {str(e)}', 'error': True})}\n\n"

    # Return the streaming response
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"  # Disable buffering in Nginx if used
        }
    )

@app.post("/research/chat")
async def chat_research(request: Dict[str, Any]):
    message = request.get("message", "")
    if not message:
        raise HTTPException(status_code=400, detail="Message is required")

    response = gemini_client.chat_generate_content(message)
    return {"content": response}

@app.post("/research/stream-chat")
async def stream_chat_research(request: Request):
    data = await request.json()
    message = data.get("message", "")
    if not message:
        raise HTTPException(status_code=400, detail="Message is required")

    async def event_generator():
        async for chunk in gemini_client.async_stream_chat_generate_content(message):
            yield f"data: {json.dumps({'content': chunk})}\n\n"

    return StreamingResponse(event_generator(), media_type="text/event-stream")

# API endpoint for browser extension
@app.post("/extension/add-snippet")
async def add_snippet_from_extension(request: Request):
    """Add a snippet from the browser extension"""
    try:
        data = await request.json()
        session_id = data.get("session_id")
        content = data.get("content")
        source_url = data.get("source_url")

        if not session_id or not content:
            logger.warning("Missing required fields for snippet from extension")
            return {"success": False, "error": "Missing required fields"}

        # Check if session exists
        db = next(get_db())
        session = db.query(models.ResearchSession).filter(models.ResearchSession.id == session_id).first()
        if not session:
            logger.warning(f"Extension tried to add snippet to non-existent session ID {session_id}")
            return {"success": False, "error": "Session not found"}

        # Create the snippet
        db_snippet = models.BrowserSnippet(
            session_id=session_id,
            content=content,
            source_url=source_url
        )
        db.add(db_snippet)
        db.commit()
        db.refresh(db_snippet)

        logger.info(f"Added snippet from extension for session ID {session_id}")
        return {
            "success": True,
            "snippet_id": db_snippet.id,
            "message": "Snippet added successfully"
        }
    except Exception as e:
        logger.error(f"Error adding snippet from extension: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {"success": False, "error": str(e)}

# API endpoint to get active sessions for the extension
@app.get("/extension/sessions")
async def get_sessions_for_extension():
    """Get all sessions for the browser extension"""
    try:
        db = next(get_db())
        sessions = db.query(models.ResearchSession).order_by(
            models.ResearchSession.updated_at.desc()
        ).limit(10).all()

        session_list = [
            {
                "id": session.id,
                "title": session.title,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat()
            }
            for session in sessions
        ]

        logger.info(f"Provided {len(session_list)} sessions to extension")
        return {"success": True, "sessions": session_list}
    except Exception as e:
        logger.error(f"Error getting sessions for extension: {str(e)}")
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)