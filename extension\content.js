// Content script for GigBus Snippet Collector
// Handles text selection, redirection point selection, and advanced text analysis

let isRedirectionMode = false;
let selectedRedirectionPoints = [];
let isAnalysisMode = false;
let currentSelectedText = '';

// Analysis operations available
const ANALYSIS_OPERATIONS = {
    'sentiment': {
        name: 'Sentiment Analysis',
        description: 'Analyze the emotional tone of the text',
        code: `
# Simple sentiment analysis
positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'happy', 'joy', 'positive', 'best', 'awesome', 'brilliant', 'perfect', 'outstanding']
negative_words = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'sad', 'angry', 'worst', 'horrible', 'disgusting', 'negative', 'poor', 'disappointing', 'frustrating']

text_lower = text.lower()
positive_count = sum(1 for word in positive_words if word in text_lower)
negative_count = sum(1 for word in negative_words if word in text_lower)

if positive_count > negative_count:
    sentiment = "Positive"
    score = positive_count - negative_count
elif negative_count > positive_count:
    sentiment = "Negative"
    score = negative_count - positive_count
else:
    sentiment = "Neutral"
    score = 0

result = f"Sentiment: {sentiment} (Score: {score})\\nPositive words found: {positive_count}\\nNegative words found: {negative_count}"
`
    },
    'wordcount': {
        name: 'Word Count Analysis',
        description: 'Count words, characters, and sentences',
        code: `
import re

words = len(text.split())
characters = len(text)
characters_no_spaces = len(text.replace(' ', ''))
sentences = len([s for s in text.split('.') if s.strip()])
paragraphs = len([p for p in text.split('\\n\\n') if p.strip()])

result = f"Words: {words}\\nCharacters: {characters}\\nCharacters (no spaces): {characters_no_spaces}\\nSentences: {sentences}\\nParagraphs: {paragraphs}"
`
    },
    'readability': {
        name: 'Readability Analysis',
        description: 'Analyze text complexity and readability',
        code: `
words = text.split()
sentences = [s for s in text.split('.') if s.strip()]
syllables = 0

# Simple syllable counting
for word in words:
    word = word.lower().strip('.,!?;:"')
    vowels = 'aeiouy'
    syllable_count = 0
    prev_was_vowel = False

    for char in word:
        if char in vowels:
            if not prev_was_vowel:
                syllable_count += 1
            prev_was_vowel = True
        else:
            prev_was_vowel = False

    if syllable_count == 0:
        syllable_count = 1
    syllables += syllable_count

avg_words_per_sentence = len(words) / max(len(sentences), 1)
avg_syllables_per_word = syllables / max(len(words), 1)

# Simple readability score (Flesch-like)
readability_score = 206.835 - (1.015 * avg_words_per_sentence) - (84.6 * avg_syllables_per_word)

if readability_score >= 90:
    level = "Very Easy"
elif readability_score >= 80:
    level = "Easy"
elif readability_score >= 70:
    level = "Fairly Easy"
elif readability_score >= 60:
    level = "Standard"
elif readability_score >= 50:
    level = "Fairly Difficult"
elif readability_score >= 30:
    level = "Difficult"
else:
    level = "Very Difficult"

result = f"Readability Score: {round(readability_score, 1)} ({level})\\nAvg words per sentence: {round(avg_words_per_sentence, 1)}\\nAvg syllables per word: {round(avg_syllables_per_word, 1)}"
`
    },
    'keywords': {
        name: 'Keyword Extraction',
        description: 'Extract most common words and phrases',
        code: `
import re

# Clean and split text
words = re.findall(r'\\b[a-zA-Z]{3,}\\b', text.lower())

# Common stop words to filter out
stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this', 'that', 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'a', 'an', 'as', 'if', 'each', 'how', 'when', 'where', 'why'}

# Filter out stop words
filtered_words = [word for word in words if word not in stop_words]

# Count word frequency
word_freq = {}
for word in filtered_words:
    word_freq[word] = word_freq.get(word, 0) + 1

# Get top 10 most common words
top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]

result = "Top Keywords:\\n" + "\\n".join([f"{word}: {count}" for word, count in top_words])
`
    }
};

// Listen for messages from popup/background
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'enableRedirectionMode') {
    enableRedirectionMode();
    sendResponse({ success: true });
  } else if (request.action === 'disableRedirectionMode') {
    disableRedirectionMode();
    sendResponse({ success: true });
  } else if (request.action === 'getSelectedText') {
    const selectedText = window.getSelection().toString();
    sendResponse({ selectedText: selectedText });
  } else if (request.action === 'getRedirectionPoints') {
    sendResponse({ points: selectedRedirectionPoints });
  } else if (request.action === 'clearRedirectionPoints') {
    clearRedirectionPoints();
    sendResponse({ success: true });
  } else if (request.action === 'performAnalysis') {
    performTextAnalysis(request.analysisType, request.text);
    sendResponse({ success: true });
  } else if (request.action === 'enableAnalysisMode') {
    enableAnalysisMode();
    sendResponse({ success: true });
  } else if (request.action === 'disableAnalysisMode') {
    disableAnalysisMode();
    sendResponse({ success: true });
  } else if (request.action === 'showAnalysisResult') {
    showAnalysisResult(request.title, request.result);
    sendResponse({ success: true });
  }
});

// Enable analysis mode
function enableAnalysisMode() {
  isAnalysisMode = true;
  document.body.style.cursor = 'help';

  // Add visual indicator
  const indicator = document.createElement('div');
  indicator.id = 'gigbus-analysis-indicator';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    left: 10px;
    background: #ff9800;
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  indicator.textContent = 'Analysis Mode: Select text and right-click for analysis options';
  document.body.appendChild(indicator);
}

// Disable analysis mode
function disableAnalysisMode() {
  isAnalysisMode = false;
  document.body.style.cursor = '';

  // Remove visual indicator
  const indicator = document.getElementById('gigbus-analysis-indicator');
  if (indicator) {
    indicator.remove();
  }
}

// Perform text analysis
function performTextAnalysis(analysisType, text) {
  if (!ANALYSIS_OPERATIONS[analysisType]) {
    showAnalysisResult('Error', 'Unknown analysis type');
    return;
  }

  const operation = ANALYSIS_OPERATIONS[analysisType];

  // Send analysis request to background script
  chrome.runtime.sendMessage({
    action: 'executeAnalysis',
    analysisType: analysisType,
    text: text,
    code: operation.code,
    operationName: operation.name
  });
}

// Show analysis result
function showAnalysisResult(title, result) {
  // Remove any existing result
  const existingResult = document.getElementById('gigbus-analysis-result');
  if (existingResult) {
    existingResult.remove();
  }

  const resultDiv = document.createElement('div');
  resultDiv.id = 'gigbus-analysis-result';
  resultDiv.style.cssText = `
    position: fixed;
    top: 60px;
    right: 10px;
    background: white;
    color: black;
    padding: 15px;
    border-radius: 8px;
    z-index: 10001;
    font-family: Arial, sans-serif;
    font-size: 12px;
    max-width: 400px;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    border: 2px solid #ff9800;
  `;

  resultDiv.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
      <strong style="color: #ff9800;">${title}</strong>
      <button id="close-analysis-result" style="background: #ff9800; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer;">×</button>
    </div>
    <div style="white-space: pre-wrap; font-family: monospace; background: #f5f5f5; padding: 8px; border-radius: 4px;">${result}</div>
    <div style="margin-top: 10px;">
      <button id="send-to-gigbus" style="background: #4285f4; color: white; border: none; border-radius: 4px; padding: 6px 12px; cursor: pointer; margin-right: 5px;">Send to GigBus</button>
      <button id="copy-result" style="background: #6c757d; color: white; border: none; border-radius: 4px; padding: 6px 12px; cursor: pointer;">Copy</button>
    </div>
  `;

  document.body.appendChild(resultDiv);

  // Add event listeners
  document.getElementById('close-analysis-result').addEventListener('click', () => {
    resultDiv.remove();
  });

  document.getElementById('send-to-gigbus').addEventListener('click', () => {
    sendAnalysisToGigBus(title, result);
  });

  document.getElementById('copy-result').addEventListener('click', () => {
    navigator.clipboard.writeText(result).then(() => {
      showTemporaryMessage('Result copied to clipboard!');
    });
  });

  // Auto-remove after 30 seconds
  setTimeout(() => {
    if (resultDiv.parentNode) {
      resultDiv.remove();
    }
  }, 30000);
}

// Send analysis result to GigBus
function sendAnalysisToGigBus(title, result) {
  chrome.runtime.sendMessage({
    action: 'sendAnalysisToGigBus',
    title: title,
    result: result
  }, (response) => {
    if (response && response.success) {
      showTemporaryMessage('Analysis sent to GigBus successfully!');
    } else {
      showTemporaryMessage('Failed to send analysis to GigBus');
    }
  });
}

// Show temporary message
function showTemporaryMessage(message) {
  const messageDiv = document.createElement('div');
  messageDiv.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #4caf50;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    z-index: 10002;
    font-family: Arial, sans-serif;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  messageDiv.textContent = message;
  document.body.appendChild(messageDiv);

  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.remove();
    }
  }, 3000);
}

// Enable redirection point selection mode
function enableRedirectionMode() {
  isRedirectionMode = true;
  document.body.style.cursor = 'crosshair';

  // Add visual indicator
  const indicator = document.createElement('div');
  indicator.id = 'gigbus-redirection-indicator';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #4285f4;
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  indicator.textContent = 'Redirection Mode: Click to select redirection points';
  document.body.appendChild(indicator);

  // Add click listener for redirection point selection
  document.addEventListener('click', handleRedirectionClick, true);
}

// Disable redirection point selection mode
function disableRedirectionMode() {
  isRedirectionMode = false;
  document.body.style.cursor = '';

  // Remove visual indicator
  const indicator = document.getElementById('gigbus-redirection-indicator');
  if (indicator) {
    indicator.remove();
  }

  // Remove click listener
  document.removeEventListener('click', handleRedirectionClick, true);
}

// Handle clicks in redirection mode
function handleRedirectionClick(event) {
  if (!isRedirectionMode) return;

  event.preventDefault();
  event.stopPropagation();

  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    const selectedText = selection.toString();

    if (selectedText.trim()) {
      // Calculate position in the full text
      const fullText = document.body.innerText;
      const beforeText = getTextBeforeRange(range);
      const position = beforeText.length;

      // Add redirection point
      const redirectionPoint = {
        position: position,
        selectedText: selectedText.trim(),
        timestamp: new Date().toISOString()
      };

      selectedRedirectionPoints.push(redirectionPoint);

      // Visual feedback
      highlightSelection(range);
      showRedirectionPointAdded(selectedText.substring(0, 50) + '...');
    }
  }
}

// Get text content before a range
function getTextBeforeRange(range) {
  const tempRange = document.createRange();
  tempRange.selectNodeContents(document.body);
  tempRange.setEnd(range.startContainer, range.startOffset);
  return tempRange.toString();
}

// Highlight selected redirection point
function highlightSelection(range) {
  const span = document.createElement('span');
  span.className = 'gigbus-redirection-highlight';
  span.style.cssText = `
    background-color: #ffeb3b;
    border: 2px solid #ff9800;
    padding: 2px;
    border-radius: 3px;
  `;

  try {
    range.surroundContents(span);
  } catch (e) {
    // If we can't surround contents, just add a marker
    const marker = document.createElement('span');
    marker.className = 'gigbus-redirection-marker';
    marker.style.cssText = `
      background-color: #ff9800;
      color: white;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 10px;
      margin: 0 2px;
    `;
    marker.textContent = '📍';
    range.insertNode(marker);
  }
}

// Show feedback when redirection point is added
function showRedirectionPointAdded(text) {
  const feedback = document.createElement('div');
  feedback.style.cssText = `
    position: fixed;
    top: 50px;
    right: 10px;
    background: #4caf50;
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 10001;
    font-family: Arial, sans-serif;
    font-size: 12px;
    max-width: 300px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  feedback.innerHTML = `
    <strong>Redirection Point Added:</strong><br>
    "${text}"
  `;
  document.body.appendChild(feedback);

  // Remove after 3 seconds
  setTimeout(() => {
    if (feedback.parentNode) {
      feedback.parentNode.removeChild(feedback);
    }
  }, 3000);
}

// Clear all redirection points
function clearRedirectionPoints() {
  selectedRedirectionPoints = [];

  // Remove all highlights
  const highlights = document.querySelectorAll('.gigbus-redirection-highlight, .gigbus-redirection-marker');
  highlights.forEach(highlight => {
    if (highlight.parentNode) {
      // For highlights, replace with text content
      if (highlight.className === 'gigbus-redirection-highlight') {
        highlight.parentNode.replaceChild(document.createTextNode(highlight.textContent), highlight);
      } else {
        // For markers, just remove
        highlight.parentNode.removeChild(highlight);
      }
    }
  });
}

// Initialize
console.log('GigBus content script loaded');
