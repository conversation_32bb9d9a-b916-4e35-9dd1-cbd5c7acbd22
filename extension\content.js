// Content script for GigBus Snippet Collector
// Handles text selection and redirection point selection

let isRedirectionMode = false;
let selectedRedirectionPoints = [];

// Listen for messages from popup/background
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'enableRedirectionMode') {
    enableRedirectionMode();
    sendResponse({ success: true });
  } else if (request.action === 'disableRedirectionMode') {
    disableRedirectionMode();
    sendResponse({ success: true });
  } else if (request.action === 'getSelectedText') {
    const selectedText = window.getSelection().toString();
    sendResponse({ selectedText: selectedText });
  } else if (request.action === 'getRedirectionPoints') {
    sendResponse({ points: selectedRedirectionPoints });
  } else if (request.action === 'clearRedirectionPoints') {
    clearRedirectionPoints();
    sendResponse({ success: true });
  }
});

// Enable redirection point selection mode
function enableRedirectionMode() {
  isRedirectionMode = true;
  document.body.style.cursor = 'crosshair';
  
  // Add visual indicator
  const indicator = document.createElement('div');
  indicator.id = 'gigbus-redirection-indicator';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #4285f4;
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  indicator.textContent = 'Redirection Mode: Click to select redirection points';
  document.body.appendChild(indicator);
  
  // Add click listener for redirection point selection
  document.addEventListener('click', handleRedirectionClick, true);
}

// Disable redirection point selection mode
function disableRedirectionMode() {
  isRedirectionMode = false;
  document.body.style.cursor = '';
  
  // Remove visual indicator
  const indicator = document.getElementById('gigbus-redirection-indicator');
  if (indicator) {
    indicator.remove();
  }
  
  // Remove click listener
  document.removeEventListener('click', handleRedirectionClick, true);
}

// Handle clicks in redirection mode
function handleRedirectionClick(event) {
  if (!isRedirectionMode) return;
  
  event.preventDefault();
  event.stopPropagation();
  
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    const selectedText = selection.toString();
    
    if (selectedText.trim()) {
      // Calculate position in the full text
      const fullText = document.body.innerText;
      const beforeText = getTextBeforeRange(range);
      const position = beforeText.length;
      
      // Add redirection point
      const redirectionPoint = {
        position: position,
        selectedText: selectedText.trim(),
        timestamp: new Date().toISOString()
      };
      
      selectedRedirectionPoints.push(redirectionPoint);
      
      // Visual feedback
      highlightSelection(range);
      showRedirectionPointAdded(selectedText.substring(0, 50) + '...');
    }
  }
}

// Get text content before a range
function getTextBeforeRange(range) {
  const tempRange = document.createRange();
  tempRange.selectNodeContents(document.body);
  tempRange.setEnd(range.startContainer, range.startOffset);
  return tempRange.toString();
}

// Highlight selected redirection point
function highlightSelection(range) {
  const span = document.createElement('span');
  span.className = 'gigbus-redirection-highlight';
  span.style.cssText = `
    background-color: #ffeb3b;
    border: 2px solid #ff9800;
    padding: 2px;
    border-radius: 3px;
  `;
  
  try {
    range.surroundContents(span);
  } catch (e) {
    // If we can't surround contents, just add a marker
    const marker = document.createElement('span');
    marker.className = 'gigbus-redirection-marker';
    marker.style.cssText = `
      background-color: #ff9800;
      color: white;
      padding: 2px 4px;
      border-radius: 3px;
      font-size: 10px;
      margin: 0 2px;
    `;
    marker.textContent = '📍';
    range.insertNode(marker);
  }
}

// Show feedback when redirection point is added
function showRedirectionPointAdded(text) {
  const feedback = document.createElement('div');
  feedback.style.cssText = `
    position: fixed;
    top: 50px;
    right: 10px;
    background: #4caf50;
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 10001;
    font-family: Arial, sans-serif;
    font-size: 12px;
    max-width: 300px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
  `;
  feedback.innerHTML = `
    <strong>Redirection Point Added:</strong><br>
    "${text}"
  `;
  document.body.appendChild(feedback);
  
  // Remove after 3 seconds
  setTimeout(() => {
    if (feedback.parentNode) {
      feedback.parentNode.removeChild(feedback);
    }
  }, 3000);
}

// Clear all redirection points
function clearRedirectionPoints() {
  selectedRedirectionPoints = [];
  
  // Remove all highlights
  const highlights = document.querySelectorAll('.gigbus-redirection-highlight, .gigbus-redirection-marker');
  highlights.forEach(highlight => {
    if (highlight.parentNode) {
      // For highlights, replace with text content
      if (highlight.className === 'gigbus-redirection-highlight') {
        highlight.parentNode.replaceChild(document.createTextNode(highlight.textContent), highlight);
      } else {
        // For markers, just remove
        highlight.parentNode.removeChild(highlight);
      }
    }
  });
}

// Initialize
console.log('GigBus content script loaded');
