# GigBus - Deep Research Assistant

A research assistant application with Gemini AI integration that helps you conduct research, save progress, and redirect your research as needed.

## Features

- Create and manage research sessions
- Generate research content using Google's Gemini AI
- Save research progress
- Add browser snippets for context
- Redirect research with new instructions
- View research history

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/gigbus.git
cd gigbus
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install the required packages:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file from the template:
```bash
cp .env.template .env
```

5. Edit the `.env` file and add your Gemini API key (get one from https://ai.google.dev/)

## Usage

1. Start the API server:
```bash
python -m uvicorn api:app --reload
```

2. In a separate terminal, start the Streamlit app:
```bash
streamlit run app.py
```

3. Open your browser and navigate to http://localhost:8501

## How to Use

1. **Create a Session**: Enter a title and click "Create Session"
2. **Research**: Enter your query and click "Start Research"
3. **Save Progress**: Click "Save Progress" to save your research
4. **Redirection**: Select a point in the text, enter new instructions, and click "Apply Redirection"
5. **Browser Snippets**: Add snippets from web pages to use as context
6. **History**: View and load previous research progress

## Browser Extension

GigBus includes a Chrome extension that allows you to collect text snippets from web pages and send them directly to your research sessions.

### Installing the Extension

1. Make sure the GigBus application is running
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" by toggling the switch in the top right corner
4. Click "Load unpacked" and select the `extension` folder
5. The extension should now be installed and visible in your toolbar

### Using the Extension

1. Click the extension icon in your toolbar
2. Select a research session from the dropdown
3. Click "Save Selection" to set this as your active session
4. On any webpage, select text you want to save
5. Right-click and select "Add to GigBus"
6. The snippet will be added to your selected session

## Project Structure

- `app.py`: Streamlit frontend application
- `api.py`: FastAPI backend server
- `models.py`: Database models
- `gemini_client.py`: Client for interacting with Google's Gemini API
- `.env`: Environment variables (API keys)
- `extension/`: Chrome extension for collecting snippets

## Requirements

- Python 3.8+
- FastAPI
- Streamlit
- SQLAlchemy
- Google Generative AI Python SDK
- Requests
- Python-dotenv

## License

MIT
